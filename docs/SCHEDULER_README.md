# NFT定时购买调度器

## 📋 功能概述

定时任务调度器可以自动监控配置的项目，并在每轮开始前自动购买NFT。

## ⚙️ 配置说明

### config.js 配置文件

```javascript
export const config = {
    // 需要定时购买的项目列表
    projects: [
        'iNxCAjhbc4L5jVEe3ajHdY28rqcNBGPpqMVkiEZZppp', // Rich
        '63B7LWnQDwAPybnjpiLrEEx1cVGG9jwxmQBc3BAMWppp', // CAI
        'EPmFmNuDgqniPgn7dV2da3EEamEGj1smyQ7PXDWHoppp',  // DOIT
        '5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp'  // Ani
    ],

    // 购买配置
    buyConfig: {
        maxNFTsPerTx: 3,        // 每个交易打包的NFT数量
        nftCountPerProject: 5,   // 每个项目购买的NFT数量
        maxRetries: 3,           // 最大重试次数
        batchMode: 'concurrent', // 批次处理模式: 'sequential' 或 'concurrent'
        advanceSeconds: 5        // 提前启动秒数
    }
};
```

## 🚀 使用方法

### 1. 通过主菜单启动

1. 运行 `node enhanced-nft-buyer.js`
2. 选择 `⏰ 定时任务管理`
3. 选择 `🚀 启动定时任务`
4. 确认启动

### 2. 直接启动调度器

```bash
node scheduler.js
```

## 📊 工作原理

### 时间计算

调度器根据项目信息计算下次购买时间：

```
下次购买时间 = last_round + sec_per_round - advanceSeconds
```

- `last_round`: 上一轮结束时间（Unix时间戳）
- `sec_per_round`: 每轮持续时间（秒）
- `advanceSeconds`: 提前启动时间（默认5秒）

### 购买流程

1. **数据获取**: 获取所有配置项目的信息和NFT列表
2. **时间计算**: 计算每个项目的下次购买时间
3. **任务调度**: 等待最近的购买时间到达
4. **执行购买**: 使用并发模式购买配置数量的NFT
5. **更新时间**: 重新计算下次购买时间
6. **循环执行**: 继续监控下一个任务

### 购买策略

- 自动选择价格最低的NFT
- 使用并发批次处理提高效率
- 支持重试机制确保成功率
- 每个交易打包多个NFT降低Gas费用

## 📁 文件结构

```
├── scheduler.js          # 主调度器脚本
├── config.js            # 配置文件
├── cache/               # 数据缓存目录
│   └── {project_mint}.json
├── logs/                # 日志目录
│   └── scheduler-{date}.log
└── enhanced-nft-buyer.js # 主程序（包含定时任务菜单）
```

## 📋 日志记录

调度器会记录以下信息：

- 购买任务执行结果
- 错误信息和堆栈跟踪
- 项目数据更新记录

日志文件按日期分割，格式为：`scheduler-YYYY-MM-DD.log`

## ⚠️ 注意事项

1. **钱包余额**: 确保钱包有足够的SOL余额
2. **网络连接**: 保持稳定的网络连接
3. **程序运行**: 不要关闭程序，调度器需要持续运行
4. **时间同步**: 确保系统时间准确
5. **项目支持**: 只支持配置文件中列出的项目

## 🔧 故障排除

### 常见问题

1. **钱包未加载**
   - 先在主程序中加载钱包
   - 确保钱包文件存在且有效

2. **项目信息获取失败**
   - 检查网络连接
   - 确认项目mint地址正确

3. **购买失败**
   - 检查钱包余额
   - 确认NFT仍然可购买
   - 查看错误日志获取详细信息

### 调试模式

修改 `config.js` 中的日志级别：

```javascript
logging: {
    enabled: true,
    logLevel: 'debug'  // 'debug', 'info', 'warn', 'error'
}
```

## 🔄 停止调度器

- 使用 `Ctrl+C` 优雅停止
- 调度器会完成当前任务后退出
- 所有日志和缓存数据会被保存
