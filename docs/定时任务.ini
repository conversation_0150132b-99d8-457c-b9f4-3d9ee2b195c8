
主菜单添加一个定时任务的菜单 ，单独创建一个脚本来实现定时启动购买 ，启动后读取  config.js 的配置 筛选出需要定时购买的 项目 
 //project_mint
 const Projects = [
            'iNxCAjhbc4L5jVEe3ajHdY28rqcNBGPpqMVkiEZZppp', // Rich
            '63B7LWnQDwAPybnjpiLrEEx1cVGG9jwxmQBc3BAMWppp', // CAI
            'EPmFmNuDgqniPgn7dV2da3EEamEGj1smyQ7PXDWHoppp',  // DOIT
            '5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp'  // Ani
        ];
        ];  

然后获取所有的NF 数据 保存都本地 。时间到了就自动开始购买 使用processBatchesConcurrently 函数购买  购买完成后 读取下一次启动时间 重新设置定时任务
 /**
     * 获取项目列表
     */
    async getProjects(options = {}) {
        const { limit = 20, page = 1, keyword = '', sort = '' } = options;
        
        try {
            const url = `${this.baseUrl}/projects?limit=${limit}&page=${page}&keyword=${encodeURIComponent(keyword)}&sort=${sort}`;
            console.log(chalk.blue(`📋 获取项目列表: ${url}`));

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'PPP-Project-NFT-Buyer/1.0.0'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            if (!data.success) {
                throw new Error('API 返回失败状态');
            }

            return data;
        } catch (error) {
            console.error(chalk.red(`获取项目列表失败: ${error.message}`));
            throw error;
        }
    }
项目信息： 根据  last_round加上 sec_per_round 为下一次的启动时间 需提前5 秒启动  
 {
            "project_pubkey": "7Ki9oDHYGvgUxEznuKXW6TVsERNukqLnf1FwimsrsCQ9",
            "bump": 253,
            "creator_pubkey": "U9cGTCZGhMEwtjoyX1cB2LL7Sq1FCsNhAsVTCB5Judy",
            "mint_pubkey": "63B7LWnQDwAPybnjpiLrEEx1cVGG9jwxmQBc3BAMWppp",
            "authority_pubkey": "Gy2g3hnQ6J9zcwaTFd7Ej4vZs2ZS6UHTN9q4KHvETwQX",
            "project_name": "Consensus of AI",
            "project_desc": "Consensus of AI.. Creativity of AI.. Culture of AI.. Common AI.. Community AI.. ",
            "token_symbol": "CAI",
            "token_uri": "https://ipfs.ppp.fun/ipfs/QmXmJrmLUUHuV7pHKLkFY2mTzGxaL1Nmqat47t9Z2o7DmV",
            "init_token_supply": 1000000000000,
            "max_token_supply": 101000000000000,
            "init_nft_supply": 100,
            "max_nft_supply": 10000,
            "token_per_nft": 10000000000,
            "init_nft_price": 100000000,
            "increase_per_round": 1000,
            "trade_fee_rate": 500,
            "sec_per_round": 28800,
            "sec_to_burn_nft": 172800,
            "nft_id": 327,
            "aid": 3722,
            "nft_issue_count": 327,
            "nft_burn_count": 25,
            "round": 25,
            "last_round": 1752823573,
            "create_time": "2025-07-09T13:51:53.000Z",
            "unix_ts": 1752097913,
            "image_url": "https://ipfs.ppp.fun/ipfs/QmZk7tVLLrRwUhKcLTQjmSoDccrfzG9PKM6Z4BAzZ941oB",
            "nft_name": "CAI",
            "nft_image_url": "https://ipfs.ppp.fun/ipfs/QmZk7tVLLrRwUhKcLTQjmSoDccrfzG9PKM6Z4BAzZ941oB",
            "bgm_url": null,
            "website": null,
            "twitter": "https://x.com/pppdotfun",
            "telegram": null,
            "discord": null,
            "market_cap": 1123.6929347556984,
            "fdv": 93388.89104763797,
            "circulation_supply": 1215272.8781459928,
            "updated_at": "2025-07-17T23:48:07.000Z",
            "project_mint": "63B7LWnQDwAPybnjpiLrEEx1cVGG9jwxmQBc3BAMWppp",
            "volume_24h": 459406865382,
            "tx_24h": 3136
        },
