# PPP NFT 批量购买模式说明

## 概述

PPP NFT 购买器现在提供两种不同的批量购买模式，满足不同的购买需求：

1. **🚀 批量购买NFT (并发)** - 传统并发模式
2. **📦 打包购买NFT (多个NFT一个交易)** - 新的打包模式

## 🚀 批量购买NFT (并发模式)

### 工作原理
- 为每个NFT创建**单独的交易**
- 使用并发处理同时发送多个交易
- 每个交易包含一个Buy指令

### 特点
- ✅ 单个NFT失败不影响其他NFT
- ✅ 可以设置并发数量 (1-10)
- ✅ 独立的重试机制
- ✅ 更高的成功率
- ❌ 交易费用较高 (每个NFT一个交易费)

### 适用场景
- 购买大量NFT (10+个)
- 网络不稳定时
- 对成功率要求高
- 不在意交易费用

### 配置选项
- **购买数量**: 1-50个NFT
- **并发数量**: 1-10 (建议3-5)
- **重试次数**: 1-10 (建议3)

### 费用示例
购买10个NFT:
- 交易数: 10个
- 交易费: 10 × ~0.000005 SOL = ~0.00005 SOL

## 📦 打包购买NFT (打包模式)

### 工作原理
- 将多个NFT的Buy指令打包到**一个交易**中
- 一个交易包含2-5个Buy指令
- 原子性操作：要么全部成功，要么全部失败

### 特点
- ✅ 显著降低交易费用
- ✅ 提高执行效率
- ✅ 原子性保证
- ❌ 单个交易失败影响整个批次
- ❌ 交易大小有限制

### 适用场景
- 购买少量NFT (2-10个)
- 网络稳定时
- 对费用敏感
- 需要原子性操作

### 配置选项
- **每个交易NFT数**: 1-5个 (建议2-4)
- **单个NFT最大价格**: 设置价格上限
- **总购买数量**: 根据可用NFT数量

### 费用示例
购买10个NFT (每个交易包含2个NFT):
- 交易数: 5个
- 交易费: 5 × ~0.000005 SOL = ~0.000025 SOL
- **节省: 50% 的交易费用**

## 📊 对比表格

| 特性 | 并发模式 | 打包模式 |
|------|----------|----------|
| 交易数量 | 每个NFT一个交易 | 多个NFT一个交易 |
| 交易费用 | 较高 | 较低 (节省50-80%) |
| 成功率 | 较高 | 中等 |
| 失败影响 | 单个NFT | 整个批次 |
| 并发控制 | 支持 | 不支持 |
| 重试机制 | 单独重试 | 批次重试 |
| 原子性 | 无 | 有 |
| 适合数量 | 大量 (10+) | 少量 (2-10) |

## 🎯 选择建议

### 选择并发模式的情况
- 购买大量NFT (10个以上)
- 网络环境不稳定
- 对成功率要求很高
- 不在意交易费用
- 需要灵活的重试策略

### 选择打包模式的情况
- 购买少量NFT (2-10个)
- 网络环境稳定
- 对交易费用敏感
- 需要原子性操作
- 希望提高执行效率

## 🚀 使用方法

### 启动程序
```bash
node enhanced-nft-buyer.js
```

### 选择模式
```
? 选择操作:
  🚀 批量购买NFT (并发)           ← 传统并发模式
  📦 打包购买NFT (多个NFT一个交易) ← 新的打包模式
```

### 并发模式流程
1. 选择项目
2. 设置购买数量
3. 设置并发数量和重试次数
4. 系统自动选择最便宜的NFT
5. 并发执行购买

### 打包模式流程
1. 选择项目
2. 设置每个交易包含的NFT数量
3. 设置价格上限和总购买数量
4. 系统自动选择最便宜的NFT
5. 创建打包交易执行

## 💡 最佳实践

### 并发模式最佳实践
- 并发数设置为3-5，避免过高
- 重试次数设置为3，平衡效率和成功率
- 监控网络状况，网络差时降低并发数

### 打包模式最佳实践
- 每个交易包含2-4个NFT，避免交易过大
- 确保钱包有足够余额支付所有NFT
- 网络拥堵时避免使用打包模式

## 🔧 技术细节

### 并发模式实现
```javascript
// 并发执行多个单独的交易
const promises = nfts.map(nft => this.buyNFT(nft));
const results = await Promise.allSettled(promises);
```

### 打包模式实现
```javascript
// 将多个Buy指令添加到一个交易中
const transaction = new Transaction();
for (const nft of nftBatch) {
    const buyInstruction = await this.createBuyInstruction(nft);
    transaction.add(buyInstruction);
}
await this.sendTransaction(transaction);
```

## 总结

两种批量购买模式各有优势，用户可以根据具体需求选择合适的模式：

- **大量购买 + 高成功率** → 选择并发模式
- **少量购买 + 低费用** → 选择打包模式

系统会保留两种模式，让用户根据实际情况灵活选择。
