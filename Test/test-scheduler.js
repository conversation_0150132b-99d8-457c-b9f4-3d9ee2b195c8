import chalk from 'chalk';
import fs from 'fs/promises';
import path from 'path';
import { config } from './config.js';

async function testScheduler() {
    console.log(chalk.blue.bold('🧪 测试调度器基本功能'));
    console.log('═'.repeat(50));

    try {
        // 测试1: 创建目录
        console.log(chalk.blue('📁 测试目录创建...'));
        const logDir = config.logging.logDir || './logs';
        const cacheDir = config.cache.dataDir || './cache';
        
        await fs.mkdir(logDir, { recursive: true });
        await fs.mkdir(cacheDir, { recursive: true });
        console.log(chalk.green('✅ 目录创建成功'));

        // 测试2: 写入状态文件
        console.log(chalk.blue('📝 测试状态文件写入...'));
        const statusFile = path.join(cacheDir, 'scheduler-status.json');
        const testStatus = {
            pid: process.pid,
            status: 'testing',
            message: '测试状态文件写入',
            startTime: Date.now(),
            lastUpdate: Date.now(),
            projectsCount: config.projects.length,
            nextTasks: []
        };

        await fs.writeFile(statusFile, JSON.stringify(testStatus, null, 2));
        console.log(chalk.green('✅ 状态文件写入成功'));

        // 测试3: 读取状态文件
        console.log(chalk.blue('📖 测试状态文件读取...'));
        const readStatus = JSON.parse(await fs.readFile(statusFile, 'utf8'));
        console.log(chalk.green('✅ 状态文件读取成功'));
        console.log(chalk.gray(`  状态: ${readStatus.status}`));
        console.log(chalk.gray(`  消息: ${readStatus.message}`));

        // 测试4: 检查配置
        console.log(chalk.blue('⚙️ 测试配置读取...'));
        console.log(chalk.green('✅ 配置读取成功'));
        console.log(chalk.gray(`  监控项目数: ${config.projects.length}`));
        console.log(chalk.gray(`  项目列表:`));
        config.projects.forEach((project, index) => {
            console.log(chalk.gray(`    ${index + 1}. ${project.substring(0, 8)}...`));
        });

        // 测试5: 模拟PPP买家初始化
        console.log(chalk.blue('🔧 测试PPP买家导入...'));
        try {
            const { PPPBuyer } = await import('./ppp-buyer.js');
            console.log(chalk.green('✅ PPP买家模块导入成功'));
            
            const buyer = new PPPBuyer();
            console.log(chalk.green('✅ PPP买家实例创建成功'));
        } catch (error) {
            console.log(chalk.red(`❌ PPP买家测试失败: ${error.message}`));
        }

        // 测试6: 模拟钱包管理器
        console.log(chalk.blue('👛 测试钱包管理器导入...'));
        try {
            const { WalletManager } = await import('./wallet-manager.js');
            console.log(chalk.green('✅ 钱包管理器模块导入成功'));
            
            const walletManager = new WalletManager();
            console.log(chalk.green('✅ 钱包管理器实例创建成功'));
        } catch (error) {
            console.log(chalk.red(`❌ 钱包管理器测试失败: ${error.message}`));
        }

        console.log(chalk.green.bold('\n🎉 基本功能测试完成'));
        console.log(chalk.blue('现在可以尝试启动完整的调度器'));

    } catch (error) {
        console.error(chalk.red(`❌ 测试失败: ${error.message}`));
        console.error(chalk.red(`错误堆栈: ${error.stack}`));
    }
}

testScheduler();
