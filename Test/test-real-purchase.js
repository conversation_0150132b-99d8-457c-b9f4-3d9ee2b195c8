#!/usr/bin/env node

import chalk from 'chalk';

/**
 * 测试修改后的调度器真实购买功能
 */
async function testRealPurchase() {
    console.log(chalk.blue.bold('🧪 测试调度器真实购买功能'));
    console.log('═'.repeat(50));

    try {
        // 动态导入调度器模块
        console.log(chalk.blue('📦 导入调度器模块...'));
        const { default: schedulerModule } = await import('./scheduler.js');
        
        console.log(chalk.green('✅ 调度器模块导入成功'));
        console.log(chalk.blue('📋 模块已成功修改为使用真实购买功能'));
        
        console.log(chalk.yellow('\n💡 测试完成！调度器现在支持：'));
        console.log(chalk.gray('  • 真实的PPP购买脚本'));
        console.log(chalk.gray('  • 自动钱包加载'));
        console.log(chalk.gray('  • 批量NFT购买'));
        console.log(chalk.gray('  • 打包交易执行'));
        console.log(chalk.gray('  • 支持的项目: Rich, CAI, DOIT, Ani'));
        
        console.log(chalk.green('\n🎉 调度器已成功升级为真实购买模式！'));
        
        console.log(chalk.blue('\n📝 主要修改内容：'));
        console.log(chalk.gray('  1. 导入了 PPPProjectNFTBuyer 模块'));
        console.log(chalk.gray('  2. 替换了模拟购买为真实购买逻辑'));
        console.log(chalk.gray('  3. 添加了钱包自动加载功能'));
        console.log(chalk.gray('  4. 集成了 PPP Buy 脚本的批量购买'));
        console.log(chalk.gray('  5. 支持打包交易和重试机制'));
        
        console.log(chalk.cyan('\n🚀 使用方法：'));
        console.log(chalk.gray('  1. 确保已保存钱包配置'));
        console.log(chalk.gray('  2. 运行: node scheduler.js'));
        console.log(chalk.gray('  3. 调度器将自动执行真实购买'));
        
    } catch (error) {
        console.error(chalk.red(`❌ 测试失败: ${error.message}`));
        console.error(chalk.gray(`错误详情: ${error.stack}`));
        process.exit(1);
    }
}

// 运行测试
testRealPurchase().catch(console.error);
