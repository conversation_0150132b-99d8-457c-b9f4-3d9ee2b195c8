#!/usr/bin/env node

import { PPPProjectNFTBuyer } from './ppp-project-nft-buyer.js';
import chalk from 'chalk';

/**
 * 快速购买脚本
 * 用于已知项目和NFT的快速购买
 * 
 * 使用方法:
 * node quick-buy.js <projectMint> <nftId> <walletPath> [rpcUrl]
 */

async function quickBuy() {
    const args = process.argv.slice(2);
    
    if (args.length < 3) {
        console.log(chalk.red('❌ 参数不足'));
        console.log(chalk.blue('使用方法:'));
        console.log(chalk.gray('node quick-buy.js <projectMint> <nftId> <walletPath> [rpcUrl]'));
        console.log(chalk.gray(''));
        console.log(chalk.gray('参数说明:'));
        console.log(chalk.gray('  projectMint  - 项目mint地址'));
        console.log(chalk.gray('  nftId        - NFT ID'));
        console.log(chalk.gray('  walletPath   - 钱包文件路径'));
        console.log(chalk.gray('  rpcUrl       - RPC URL (可选，默认主网)'));
        console.log(chalk.gray(''));
        console.log(chalk.gray('示例:'));
        console.log(chalk.gray('node quick-buy.js H23p5B7weYq9ACuQ4Cjg42uPFoJRn4ZXrd1jTa1ZSppp 123 ./wallet.json'));
        process.exit(1);
    }

    const [projectMint, nftId, walletPath, rpcUrl = 'https://api.mainnet-beta.solana.com'] = args;

    console.log(chalk.blue.bold('⚡ PPP快速购买工具\n'));
    console.log(chalk.gray(`项目: ${projectMint}`));
    console.log(chalk.gray(`NFT ID: ${nftId}`));
    console.log(chalk.gray(`钱包: ${walletPath}`));
    console.log(chalk.gray(`网络: ${rpcUrl}\n`));

    const buyer = new PPPProjectNFTBuyer();

    try {
        // 1. 初始化
        console.log(chalk.blue('🔗 初始化连接...'));
        await buyer.initialize(rpcUrl, walletPath);

        if (!buyer.wallet) {
            throw new Error('钱包加载失败，无法执行购买');
        }

        // 2. 获取NFT信息
        console.log(chalk.blue('🎨 获取NFT信息...'));
        const nftResult = await buyer.getProjectNFTs(projectMint, { 
            nft_id: nftId, 
            limit: 1 
        });

        if (!nftResult.data || nftResult.data.length === 0) {
            throw new Error(`未找到NFT ID: ${nftId}`);
        }

        const nft = nftResult.data[0];
        
        if (nft.is_burned === 1) {
            throw new Error('NFT已被销毁，无法购买');
        }

        // 3. 显示NFT信息
        console.log(chalk.green.bold('\n✅ 找到目标NFT:'));
        buyer.displayNFT(nft, 0);

        // 4. 确认购买
        const priceSol = nft.price / 1000000000;
        console.log(chalk.yellow.bold(`\n💰 购买价格: ${priceSol.toFixed(6)} SOL`));
        
        // 检查余额
        const balance = await buyer.connection.getBalance(buyer.wallet.publicKey);
        const balanceSol = balance / 1000000000;
        
        console.log(chalk.blue(`💳 当前余额: ${balanceSol.toFixed(6)} SOL`));
        
        if (balance < nft.price + 0.01 * 1000000000) { // 预留0.01 SOL作为手续费
            throw new Error('余额不足，无法完成购买');
        }

        // 5. 执行购买
        console.log(chalk.blue.bold('\n🛒 执行购买...'));
        
        const result = await buyer.buyNFT(nft, projectMint);
        
        if (result) {
            console.log(chalk.green.bold('\n🎉 购买成功!'));
            console.log(chalk.blue(`交易签名: ${result.signature}`));
            console.log(chalk.blue(`NFT地址: ${result.nftPubkey}`));
            console.log(chalk.blue(`购买金额: ${result.amount} SOL`));
            console.log(chalk.blue(`查看交易: https://solscan.io/tx/${result.signature}`));
            
            // 保存购买记录
            const purchaseRecord = {
                timestamp: new Date().toISOString(),
                project_mint: projectMint,
                nft_id: nftId,
                nft: nft,
                transaction: result,
                buyer: buyer.wallet.publicKey.toString(),
                quick_buy: true
            };
            
            const filename = `quick-buy-${nftId}-${Date.now()}.json`;
            buyer.saveData(purchaseRecord, filename);
            
        } else {
            console.log(chalk.yellow('❌ 购买被取消或失败'));
        }

    } catch (error) {
        console.error(chalk.red(`❌ 快速购买失败: ${error.message}`));
        process.exit(1);
    }
}

// 运行快速购买
quickBuy().catch(console.error);
