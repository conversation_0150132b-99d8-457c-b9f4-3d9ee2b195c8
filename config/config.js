// PPP项目配置文件
export const CONFIG = {
  // Solana网络配置

  // RPC_URL: 'https://late-winter-putty.solana-mainnet.quiknode.pro/de4eef0ab926faaba4e910a16056b6ce5ecd4ffe',
  RPC_URL: 'https://convincing-weathered-breeze.solana-mainnet.quiknode.pro/67f6132da9d6570ad0ee79990e82aa36df8f15a9',
  // RPC_URL: 'https://responsive-few-feather.solana-mainnet.quiknode.pro/7d9a947557b9aa76575b5db837518270a444c621',
  // RPC_URL: 'https://mainnet.helius-rpc.com/?api-key=ad4e959d-bfb8-46b2-be2f-baf0fa9321ad',
  // RPC_URL: 'https://mainnet.helius-rpc.com/?api-key=4b5e8887-653a-4487-b85f-14861ae20a34',
  DEVNET_RPC_URL: 'https://api.devnet.solana.com',
  
  // PPP程序配置
  PPP_PROGRAM_ID: 'PPP5a1E95YMFv8Rmhvc7PBW6SSfHfvNKfAz7988xvB3',
  
  // API配置
  PPP_API_BASE_URL: 'https://api.ppp.fun',
  
  // 钱包配置
  MIN_SOL_BALANCE: 0.01, // 最小SOL余额（用于交易费用）
  
  // 交易配置
  TRANSACTION_TIMEOUT: 60000, // 交易超时时间（毫秒）
  CONFIRMATION_COMMITMENT: 'confirmed',
  
  // 常用代币地址
  TOKENS: {
    SOL: {
      mint: 'So11111111111111111111111111111111111111112',
      decimals: 9,
      symbol: 'SOL'
    },
    WSOL: {
      mint: 'So11111111111111111111111111111111111111112',
      decimals: 9,
      symbol: 'WSOL'
    }
  },
  
  // 程序地址
  PROGRAMS: {
    TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
    SYSTEM_PROGRAM: '11111111111111111111111111111111',
    RAYDIUM_AMM: 'CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C'
  },
  
  // 默认查询参数
  DEFAULT_QUERY_PARAMS: {
    PROJECT_LIMIT: 20,
    NFT_LIMIT: 100,
    SORT_ORDER: 'asc'
  },
  
  // 费用配置
  FEES: {
    PROTOCOL_FEE_RATE: 0.01, // 1%
    TRADE_FEE_RATE: 0.05,    // 5%
    NETWORK_FEE: 0.00005     // 网络费用（SOL）
  }
};

// 环境配置
export const getConfig = (env = 'mainnet') => {
  const config = { ...CONFIG };
  
  if (env === 'devnet') {
    config.RPC_URL = config.DEVNET_RPC_URL;
  }
  
  return config;
};
