import chalk from 'chalk';
import fs from 'fs/promises';
import path from 'path';
import { config } from './config.js';

class SimpleScheduler {
    constructor() {
        this.isRunning = false;
        this.statusFile = path.join('./cache', 'scheduler-status.json');
        this.startTime = Date.now();
        this.logDir = './logs';
        this.cacheDir = './cache';
    }

    /**
     * 初始化调度器
     */
    async initialize() {
        console.log(chalk.blue.bold('🚀 简化版NFT定时购买调度器启动'));
        console.log('═'.repeat(50));

        try {
            // 创建必要的目录
            console.log(chalk.blue('📁 创建必要目录...'));
            await fs.mkdir(this.logDir, { recursive: true });
            await fs.mkdir(this.cacheDir, { recursive: true });

            // 写入初始状态
            await this.updateStatus('initializing', '调度器正在初始化...');

            console.log(chalk.green('✅ 调度器初始化完成'));

            this.isRunning = true;
            await this.updateStatus('running', '调度器已启动，模拟运行中');

            // 开始模拟调度
            await this.startSimulation();

        } catch (error) {
            console.error(chalk.red(`❌ 初始化失败: ${error.message}`));
            console.error(chalk.red(`错误堆栈: ${error.stack}`));
            
            try {
                await this.updateStatus('error', `初始化失败: ${error.message}`);
            } catch (statusError) {
                console.error(chalk.red(`❌ 写入错误状态失败: ${statusError.message}`));
            }
            
            process.exit(1);
        }
    }

    /**
     * 更新状态文件 - 使用原子写入避免JSON损坏
     */
    async updateStatus(status, message, additionalData = {}) {
        try {
            const statusData = {
                pid: process.pid,
                status: status,
                message: message,
                startTime: this.startTime,
                lastUpdate: Date.now(),
                projectsCount: config.projects.length,
                nextTasks: this.getSimulatedTasks(),
                ...additionalData
            };

            // 使用临时文件进行原子写入
            const tempFile = this.statusFile + '.tmp';
            const jsonContent = JSON.stringify(statusData, null, 2);

            // 先写入临时文件
            await fs.writeFile(tempFile, jsonContent, 'utf8');

            // 然后重命名为目标文件（原子操作）
            await fs.rename(tempFile, this.statusFile);

            console.log(chalk.gray(`📝 状态已更新: ${status} - ${message}`));
        } catch (error) {
            console.warn(chalk.yellow(`⚠️ 更新状态文件失败: ${error.message}`));

            // 如果临时文件存在，尝试清理
            try {
                const tempFile = this.statusFile + '.tmp';
                await fs.unlink(tempFile);
            } catch (cleanupError) {
                // 忽略清理错误
            }
        }
    }

    /**
     * 获取模拟任务信息
     */
    getSimulatedTasks() {
        const now = Date.now();
        return config.projects.map((projectMint, index) => {
            const nextTime = now + (index + 1) * 60000; // 每个项目间隔1分钟
            return {
                projectMint: projectMint.substring(0, 8) + '...',
                projectName: `Project ${index + 1}`,
                nextBuyTime: nextTime,
                nextBuyTimeStr: new Date(nextTime).toLocaleString()
            };
        });
    }

    /**
     * 开始模拟调度
     */
    async startSimulation() {
        console.log(chalk.blue.bold('\n⏰ 开始模拟调度'));
        console.log('═'.repeat(50));

        let cycle = 0;
        while (this.isRunning) {
            try {
                cycle++;
                
                // 模拟等待状态
                await this.updateStatus('waiting', `模拟等待中... (周期 ${cycle})`, {
                    cycle: cycle,
                    simulationMode: true
                });
                
                console.log(chalk.blue(`⏳ 模拟等待 30 秒... (周期 ${cycle})`));
                await this.sleep(30000);

                if (!this.isRunning) break;

                // 模拟购买状态
                const projectIndex = (cycle - 1) % config.projects.length;
                const projectMint = config.projects[projectIndex];
                
                await this.updateStatus('buying', `模拟购买项目 ${projectIndex + 1}`, {
                    currentProject: projectMint,
                    cycle: cycle,
                    simulationMode: true
                });

                console.log(chalk.green(`🛒 模拟购买项目 ${projectIndex + 1}: ${projectMint.substring(0, 8)}...`));
                await this.sleep(10000); // 模拟购买过程

                // 模拟购买完成
                await this.updateStatus('running', `项目 ${projectIndex + 1} 模拟购买完成`, {
                    lastBuyResult: {
                        projectMint,
                        projectName: `Project ${projectIndex + 1}`,
                        successCount: Math.floor(Math.random() * 5) + 1,
                        totalNFTs: 5,
                        timestamp: Date.now(),
                        simulated: true
                    },
                    cycle: cycle
                });

                console.log(chalk.green(`✅ 项目 ${projectIndex + 1} 模拟购买完成`));

            } catch (error) {
                console.error(chalk.red(`❌ 模拟调度错误: ${error.message}`));
                await this.updateStatus('error', `模拟错误: ${error.message}`);
                await this.sleep(10000);
            }
        }
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 停止调度器
     */
    async stop() {
        this.isRunning = false;
        await this.updateStatus('stopped', '调度器已停止');
        console.log(chalk.yellow('⏹️ 调度器已停止'));
    }
}

// 启动调度器
const scheduler = new SimpleScheduler();

// 处理程序退出
process.on('SIGINT', async () => {
    console.log(chalk.yellow('\n📴 接收到退出信号，正在停止调度器...'));
    await scheduler.stop();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log(chalk.yellow('\n📴 接收到终止信号，正在停止调度器...'));
    await scheduler.stop();
    process.exit(0);
});

// 启动
scheduler.initialize().catch(async error => {
    console.error(chalk.red(`❌ 调度器启动失败: ${error.message}`));
    try {
        await scheduler.updateStatus('error', `启动失败: ${error.message}`);
    } catch (statusError) {
        console.error(chalk.red(`❌ 写入错误状态失败: ${statusError.message}`));
    }
    process.exit(1);
});
