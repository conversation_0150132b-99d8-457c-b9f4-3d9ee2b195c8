#!/usr/bin/env node

import { PPPProjectNFTBuyer } from './ppp-project-nft-buyer.js';
import { CONFIG, getConfig } from './config/config.js';
import chalk from 'chalk';
import inquirer from 'inquirer';
import fs from 'fs';

/**
 * 增强版PPP项目NFT购买工具
 * 集成钱包管理、项目浏览、NFT查询和购买功能
 */

class EnhancedNFTBuyer {
    constructor() {
        this.buyer = new PPPProjectNFTBuyer();
        this.config = CONFIG;
    }

    /**
     * 主菜单
     */
    async showMainMenu() {
        console.log(chalk.blue.bold('🎯 PPP项目NFT购买工具 v0.1\n'));

        // 显示当前钱包状态
        const walletInfo = await this.buyer.getWalletInfo();
        if (walletInfo) {
            console.log(chalk.green(`💼 当前钱包: ${walletInfo.address.substring(0, 8)}...${walletInfo.address.substring(-8)}`));
            console.log(chalk.green(`💰 余额: ${walletInfo.solBalance.toFixed(4)} SOL\n`));
        } else {
            const hasSaved = this.buyer.walletManager.hasSavedWallet();
            if (hasSaved) {
                console.log(chalk.yellow('💼 钱包未加载 (有保存的钱包可用)\n'));
            } else {
                console.log(chalk.yellow('💼 钱包未初始化\n'));
            }
        }

        const { action } = await inquirer.prompt([
            {
                type: 'list',
                name: 'action',
                message: '选择操作:',
                choices: [
                    { name: '🔑 钱包管理', value: 'wallet' },
                    // { name: '� 批量购买NFT (并发)', value: 'batch-buy' },
                    { name: '📦 打包购买NFT (多个NFT一个交易)', value: 'packed-buy' },
                    // { name: '⏰ 定时任务管理', value: 'scheduler' },
                    // { name: '📋 浏览项目', value: 'projects' },
                    // { name: '🎨 查看NFT', value: 'nfts' },
                    { name: '📊 钱包信息', value: 'info' },
                    { name: '⚙️  设置', value: 'settings' },
                    { name: '❌ 退出', value: 'exit' }
                ]
            }
        ]);

        switch (action) {
            case 'wallet':
                await this.walletMenu();
                break;
            case 'buy':
                await this.buyMenu();
                break;
            case 'batch-buy':
                await this.batchBuyMenu();
                break;
            case 'packed-buy':
                await this.packedBuyMenu();
                break;
            case 'scheduler':
                await this.schedulerMenu();
                break;
            case 'projects':
                await this.projectsMenu();
                break;
            case 'nfts':
                await this.nftsMenu();
                break;
            case 'info':
                await this.showWalletInfo();
                break;
            case 'settings':
                await this.settingsMenu();
                break;
            case 'exit':
                console.log(chalk.blue('👋 再见!'));
                process.exit(0);
                break;
        }

        // 返回主菜单
        await this.showMainMenu();
    }
    /**
     * 购买菜单
     * 
     */
    async buyMenu() {
        console.log(chalk.blue.bold('\n🛒 NFT购买\n'));

        const walletInfo = await this.buyer.getWalletInfo();
        if (!walletInfo) {
            console.log(chalk.red('❌ 请先初始化钱包'));
            return;
        }

        // 选择项目
        const project = await this.selectProject();
        if (!project) return;

        // 选择NFT
        const nft = await this.selectNFT(project.project_mint);
        if (!nft) return;

        // 执行购买
        await this.executePurchase(nft, project);
    }

    /**
     * 批量购买NFT菜单 (并发模式)
     */
    async batchBuyMenu() {
        console.log(chalk.blue.bold('\n� 批量购买NFT (并发模式)'));
        console.log('═'.repeat(40));

        // 检查钱包
        if (!this.buyer.walletManager.getAddress()) {
            console.log(chalk.red('❌ 请先加载钱包'));
            await this.showMainMenu();
            return;
        }

        // 选择项目
        const project = await this.selectProject();
        if (!project) {
            await this.showMainMenu();
            return;
        }

        console.log(chalk.blue(`\n🎯 项目: ${project.project_name}`));
        console.log(`代币: ${project.project_mint}`);

        // 询问并发配置
        const { buyCount, concurrency, maxRetries } = await inquirer.prompt([
            {
                type: 'number',
                name: 'buyCount',
                message: '输入要购买的NFT数量:',
                default: 1,
                validate: (value) => {
                    if (!value || value <= 0) return '购买数量必须大于0';
                    if (value > 50) return '单次最多购买50个NFT';
                    return true;
                }
            },
            {
                type: 'number',
                name: 'concurrency',
                message: '并发购买数量 (建议1-5):',
                default: 3,
                validate: (value) => {
                    if (!value || value <= 0) return '并发数量必须大于0';
                    if (value > 10) return '并发数量不建议超过10';
                    return true;
                }
            },
            {
                type: 'number',
                name: 'maxRetries',
                message: '失败重试次数:',
                default: 3,
                validate: (value) => {
                    if (!value || value <= 0) return '重试次数必须大于0';
                    if (value > 10) return '重试次数不建议超过10';
                    return true;
                }
            }
        ]);

        // 直接执行并发批量购买
        console.log(chalk.blue.bold(`\n🚀 开始执行并发批量购买 ${buyCount} 个NFT...`));
        console.log(chalk.gray(`项目: ${project.project_name} (${project.token_symbol})`));
        console.log(chalk.gray(`Mint: ${project.project_mint}`));
        console.log(chalk.gray(`并发数: ${concurrency}, 重试次数: ${maxRetries}`));

        // 执行并发批量购买
        await this.executeBatchPurchase(project, buyCount, { concurrency, maxRetries });

        await this.showMainMenu();
    }

    /**
     * 打包购买NFT菜单 (多个NFT一个交易)
     */
    async packedBuyMenu() {
        console.log(chalk.blue.bold('\n📦 打包购买NFT (多个NFT一个交易)'));
        console.log('═'.repeat(40));

        // 检查钱包
        if (!this.buyer.walletManager.getAddress()) {
            console.log(chalk.red('❌ 请先加载钱包'));
            await this.showMainMenu();
            return;
        }

        // 选择项目
        const project = await this.selectProject();
        if (!project) {
            await this.showMainMenu();
            return;
        }

        console.log(chalk.blue(`\n🎯 项目: ${project.project_name}`));
        console.log(`代币: ${project.project_mint}`);

        // 获取NFT列表 - 支持分页获取所有NFT
        console.log(chalk.blue('\n🔍 获取NFT列表...'));
        let allNFTs = [];
        let page = 1;
        const limit = 100; // 每页100个

        while (true) {
            // console.log(chalk.gray(`  📄 获取第 ${page} 页...`));
            const nftResult = await this.buyer.getProjectNFTs(project.project_mint, {
                limit: limit,
                page: page,
                sort: 'price',
                order: 'asc'
            });

            const pageNFTs = nftResult.data || [];
            if (pageNFTs.length === 0) {
                break; // 没有更多NFT了
            }

            allNFTs = allNFTs.concat(pageNFTs);
            // console.log(chalk.gray(`  ✅ 第 ${page} 页获取到 ${pageNFTs.length} 个NFT`));

            // 如果这一页的NFT数量少于limit，说明已经是最后一页
            if (pageNFTs.length < limit) {
                break;
            }

            page++;

            // 防止无限循环，最多获取10页
            if (page > 10) {
                console.log(chalk.yellow('⚠️ 已达到最大页数限制，停止获取'));
                break;
            }
        }

        const nfts = allNFTs;
        console.log(chalk.green(`📊 总共获取到：${nfts.length} 个NFT`));

        if (!nfts || nfts.length === 0) {
            console.log(chalk.yellow('❌ 该项目没有可购买的NFT'));
            await this.showMainMenu();
            return;
        }

        // 过滤可购买的NFT (活跃、价格 > 0、有owner、不是自己的)
        const buyableNFTs = nfts.filter(nft =>
            // nft.is_burned === 0 &&  // 活跃的NFT
            nft.price > 0 &&
            nft.owner_pubkey &&
            nft.owner_pubkey !== this.buyer.walletManager.getAddress()
        );

        if (buyableNFTs.length === 0) {
            console.log(chalk.yellow('❌ 该项目没有可购买的NFT'));
            await this.showMainMenu();
            return;
        }

        // console.log(chalk.green(`✅ 找到 ${buyableNFTs.length} 个可购买的NFT`));

        // 批量购买配置
        const { maxNFTsPerTx, nftCount, batchMode } = await inquirer.prompt([
            {
                type: 'number',
                name: 'maxNFTsPerTx',
                message: '每个交易打包多少个NFT? (建议2-4个)',
                default: 3,
                validate: (value) => {
                    if (value < 1 || value > 5) {
                        return '每个交易的NFT数量必须在 1-5 之间';
                    }
                    return true;
                }
            },
            {
                type: 'number',
                name: 'nftCount',
                message: `要购买多少个NFT? (最多 ${buyableNFTs.length} 个)`,
                default: Math.min(4, buyableNFTs.length),
                validate: (value) => {
                    if (value < 1 || value > buyableNFTs.length) {
                        return `数量必须在 1-${buyableNFTs.length} 之间`;
                    }
                    return true;
                }
            },
            {
                type: 'list',
                name: 'batchMode',
                message: '批次处理模式:',
                choices: [
                    { name: '🔄 逐个处理批次 ', value: 'sequential' },
                    { name: '⚡ 并发处理批次 (快速)', value: 'concurrent' }
                ],
                default: 'concurrent'
            }
        ]);

        // 按价格排序，选择最便宜的NFT（不限制价格）
        const affordableNFTs = buyableNFTs
            .sort((a, b) => a.price - b.price)
            .slice(0, nftCount);

        // 显示选中的NFT
        // console.log(chalk.blue.bold('\n📋 选中的NFT:'));
        let totalPrice = 0;
        affordableNFTs.forEach((nft, index) => {
            const priceSol = nft.price / 1000000000;
            totalPrice += priceSol;
            console.log(`${index + 1}. NFT ${nft.nft_id} - ${priceSol.toFixed(6)} SOL`);
        });

        console.log(chalk.blue.bold(`\n💰 总价格: ${totalPrice.toFixed(6)} SOL`));
        console.log(chalk.blue(`📦 将创建 ${Math.ceil(affordableNFTs.length / maxNFTsPerTx)} 个打包交易`));
        console.log(chalk.gray(`每个交易包含最多 ${maxNFTsPerTx} 个NFT`));
        console.log(chalk.gray(`处理模式: ${batchMode === 'concurrent' ? '⚡ 并发处理' : '🔄 逐个处理'}`));

        // 直接执行批量购买，无需确认
        console.log(chalk.green(`\n🚀 开始执行批量打包购买...`));
        await this.executeBatchBuy(affordableNFTs, project, { maxNFTsPerTx, batchMode });

        await this.showMainMenu();
    }

    /**
     * 定时任务管理菜单
     */
    async schedulerMenu() {
        console.log(chalk.blue.bold('\n⏰ 定时任务管理'));
        console.log('═'.repeat(40));

        const { action } = await inquirer.prompt([
            {
                type: 'list',
                name: 'action',
                message: '选择操作:',
                choices: [
                    { name: '🚀 启动定时任务', value: 'start' },
                    { name: '📊 查看任务状态', value: 'status' },
                    { name: '📖 手动启动说明', value: 'manual' },
                    { name: '⚙️ 配置管理', value: 'config' },
                    { name: '📋 查看日志', value: 'logs' },
                    { name: '❌ 返回主菜单', value: 'back' }
                ]
            }
        ]);

        switch (action) {
            case 'start':
                await this.startScheduler();
                break;
            case 'status':
                await this.showSchedulerStatus();
                break;
            case 'manual':
                await this.showManualStartInstructions();
                break;
            case 'config':
                await this.manageSchedulerConfig();
                break;
            case 'logs':
                await this.showSchedulerLogs();
                break;
            case 'back':
                return;
        }

        await this.schedulerMenu();
    }

    /**
     * 启动定时任务
     */
    async startScheduler() {
        console.log(chalk.blue.bold('\n🚀 启动定时任务'));
        console.log('═'.repeat(40));

        // 检查钱包
        if (!this.buyer.walletManager.getAddress()) {
            console.log(chalk.red('❌ 请先加载钱包'));
            return;
        }

        console.log(chalk.yellow('⚠️ 定时任务将在后台运行，请确保：'));
        console.log(chalk.gray('  1. 钱包有足够的SOL余额'));
        console.log(chalk.gray('  2. 网络连接稳定'));
        console.log(chalk.gray('  3. 不要关闭程序'));

        const { confirm } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: '确认启动定时任务?',
                default: false
            }
        ]);

        if (!confirm) {
            console.log(chalk.yellow('❌ 已取消启动'));
            return;
        }

        try {
            // 启动定时任务脚本
            console.log(chalk.blue('🔄 正在启动定时任务...'));

            // 使用 spawn 启动调度器，但不等待
            const { spawn } = await import('child_process');
            const schedulerProcess = spawn('node', ['scheduler.js'], {
                detached: true,
                stdio: 'ignore', // 完全忽略输入输出
                cwd: process.cwd(),
                env: process.env
            });

            // 立即分离进程
            schedulerProcess.unref();

            console.log(chalk.green('✅ 定时任务已启动'));
            console.log(chalk.gray('任务将在后台运行，可以通过"查看任务状态"监控进度'));

            // 等待几秒钟让调度器初始化
            console.log(chalk.blue('⏳ 等待调度器初始化...'));
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 检查调度器是否成功启动
            const fs = await import('fs/promises');
            const path = await import('path');
            const statusFile = path.default.join('./cache', 'scheduler-status.json');

            try {
                await fs.default.access(statusFile);
                const statusData = JSON.parse(await fs.default.readFile(statusFile, 'utf8'));
                console.log(chalk.green(`📊 调度器状态: ${statusData.status}`));
                console.log(chalk.gray(`进程ID: ${statusData.pid}`));
            } catch (statusError) {
                console.log(chalk.yellow('⚠️ 无法读取调度器状态，但进程可能已启动'));
            }

        } catch (error) {
            console.log(chalk.red(`❌ 启动失败: ${error.message}`));
            console.log(chalk.yellow('💡 您可以手动运行: node simple-scheduler.js'));
        }
    }

    /**
     * 显示手动启动说明
     */
    async showManualStartInstructions() {
        console.log(chalk.blue.bold('\n📖 手动启动定时任务'));
        console.log('═'.repeat(40));

        console.log(chalk.yellow('如果自动启动遇到问题，您可以手动启动调度器：'));
        console.log('');
        console.log(chalk.green('1. 打开新的命令行窗口'));
        console.log(chalk.green('2. 切换到项目目录'));
        console.log(chalk.green('3. 运行命令:'));
        console.log(chalk.cyan('   node simple-scheduler.js'));
        console.log('');
        console.log(chalk.blue('手动启动后，您可以：'));
        console.log(chalk.gray('  • 通过"查看任务状态"监控运行情况'));
        console.log(chalk.gray('  • 使用 Ctrl+C 停止调度器'));
        console.log(chalk.gray('  • 调度器会自动保存状态到 cache/scheduler-status.json'));
        console.log('');
        console.log(chalk.yellow('💡 提示: 手动启动通常更稳定，推荐使用'));
    }

    /**
     * 查看任务状态
     */
    async showSchedulerStatus() {
        console.log(chalk.blue.bold('\n📊 定时任务状态'));
        console.log('═'.repeat(40));

        try {
            const fs = await import('fs/promises');
            const path = await import('path');

            const statusFile = path.default.join('./cache', 'scheduler-status.json');

            // 检查状态文件是否存在
            try {
                await fs.default.access(statusFile);
            } catch (error) {
                console.log(chalk.yellow('⚠️ 未找到调度器状态文件'));
                console.log(chalk.gray('可能的原因：'));
                console.log(chalk.gray('  • 调度器未启动'));
                console.log(chalk.gray('  • 调度器刚启动还未写入状态'));
                console.log(chalk.gray('  • 状态文件被删除'));
                return;
            }

            // 读取状态文件，添加重试机制
            let statusData;
            let retryCount = 0;
            const maxRetries = 3;

            while (retryCount < maxRetries) {
                try {
                    const fileContent = await fs.default.readFile(statusFile, 'utf8');

                    // 检查文件内容是否为空
                    if (!fileContent.trim()) {
                        throw new Error('状态文件为空');
                    }

                    statusData = JSON.parse(fileContent);
                    break; // 成功读取，跳出循环

                } catch (parseError) {
                    retryCount++;
                    console.log(chalk.yellow(`⚠️ 读取状态文件失败 (尝试 ${retryCount}/${maxRetries}): ${parseError.message}`));

                    if (retryCount < maxRetries) {
                        console.log(chalk.gray('等待1秒后重试...'));
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    } else {
                        throw new Error(`读取状态文件失败，已重试${maxRetries}次: ${parseError.message}`);
                    }
                }
            }

            // 检查进程是否还在运行
            const isProcessRunning = await this.checkProcessRunning(statusData.pid);

            // 检查状态文件是否是最近更新的（5分钟内）
            const timeSinceUpdate = Date.now() - statusData.lastUpdate;
            const isRecentlyActive = timeSinceUpdate < 5 * 60 * 1000; // 5分钟

            // 综合判断进程状态
            let processStatus;
            if (statusData.status === 'stopped') {
                processStatus = chalk.red('⏹️ 已停止');
            } else if (isProcessRunning && isRecentlyActive) {
                processStatus = chalk.green('✅ 运行中');
            } else if (isProcessRunning && !isRecentlyActive) {
                processStatus = chalk.yellow('⚠️ 可能卡住');
            } else {
                processStatus = chalk.red('❌ 已停止');
            }

            // 显示基本信息
            console.log(chalk.blue('📋 基本信息:'));
            console.log(`  进程ID: ${statusData.pid}`);
            console.log(`  进程状态: ${processStatus}`);
            console.log(`  启动时间: ${new Date(statusData.startTime).toLocaleString()}`);
            console.log(`  运行时长: ${this.formatDuration(Date.now() - statusData.startTime)}`);
            console.log(`  最后更新: ${new Date(statusData.lastUpdate).toLocaleString()}`);

            if (!isRecentlyActive) {
                console.log(chalk.yellow(`  ⚠️ 状态文件已 ${Math.round(timeSinceUpdate / 1000)} 秒未更新`));
            }

            // 显示当前状态
            console.log(chalk.blue('\n🔄 当前状态:'));
            const statusIcon = this.getStatusIcon(statusData.status);
            console.log(`  状态: ${statusIcon} ${statusData.status.toUpperCase()}`);
            console.log(`  消息: ${statusData.message}`);

            // 显示监控项目
            console.log(chalk.blue(`\n📊 监控项目: ${statusData.projectsCount} 个`));

            // 显示下次任务
            if (statusData.nextTasks && statusData.nextTasks.length > 0) {
                console.log(chalk.blue('\n⏰ 即将执行的任务:'));
                statusData.nextTasks.forEach((task, index) => {
                    const timeUntil = task.nextBuyTime - Date.now();
                    const timeStr = timeUntil > 0 ?
                        `${Math.round(timeUntil / 1000)} 秒后` :
                        chalk.red('已过期');
                    console.log(`  ${index + 1}. ${task.projectName} (${task.projectMint})`);
                    console.log(`     执行时间: ${task.nextBuyTimeStr} (${timeStr})`);
                });
            }

            // 显示最后购买结果
            if (statusData.lastBuyResult) {
                const result = statusData.lastBuyResult;
                console.log(chalk.blue('\n💰 最后购买结果:'));
                console.log(`  项目: ${result.projectName}`);
                console.log(`  时间: ${new Date(result.timestamp).toLocaleString()}`);
                console.log(`  结果: ${chalk.green(`成功 ${result.successCount}/${result.totalNFTs}`)}`);
            }

            // 显示最后错误
            if (statusData.lastError) {
                const error = statusData.lastError;
                console.log(chalk.blue('\n❌ 最后错误:'));
                console.log(`  项目: ${error.projectName}`);
                console.log(`  时间: ${new Date(error.timestamp).toLocaleString()}`);
                console.log(`  错误: ${chalk.red(error.error)}`);
            }

            // 显示等待信息
            if (statusData.waitingFor) {
                console.log(chalk.blue('\n⏳ 等待信息:'));
                console.log(`  等待项目: ${statusData.waitingFor}`);
                console.log(`  剩余时间: ${statusData.waitTimeSeconds} 秒`);
            }

        } catch (error) {
            console.log(chalk.red(`❌ 读取状态失败: ${error.message}`));
        }
    }

    /**
     * 配置管理
     */
    async manageSchedulerConfig() {
        console.log(chalk.blue.bold('\n⚙️ 定时任务配置'));
        console.log('═'.repeat(40));

        console.log(chalk.yellow('🔄 功能开发中...'));
        console.log(chalk.gray('将支持：'));
        console.log(chalk.gray('  • 修改监控项目列表'));
        console.log(chalk.gray('  • 调整购买参数'));
        console.log(chalk.gray('  • 设置提前启动时间'));
    }

    /**
     * 查看日志
     */
    async showSchedulerLogs() {
        console.log(chalk.blue.bold('\n📋 定时任务日志'));
        console.log('═'.repeat(40));

        console.log(chalk.yellow('🔄 功能开发中...'));
        console.log(chalk.gray('将显示：'));
        console.log(chalk.gray('  • 任务执行日志'));
        console.log(chalk.gray('  • 购买成功/失败记录'));
        console.log(chalk.gray('  • 错误信息'));
    }

    /**
     * 检查进程是否运行
     */
    async checkProcessRunning(pid) {
        try {
            // 在Windows上使用tasklist，在Unix上使用ps
            const { exec } = await import('child_process');
            const { promisify } = await import('util');
            const execAsync = promisify(exec);

            if (process.platform === 'win32') {
                try {
                    const { stdout } = await execAsync(`tasklist /FI "PID eq ${pid}" /NH`);
                    // 检查输出是否包含进程信息（不是"INFO: No tasks..."）
                    return !stdout.includes('INFO: No tasks') && stdout.trim().length > 0;
                } catch (error) {
                    // 如果命令失败，进程可能不存在
                    return false;
                }
            } else {
                try {
                    await execAsync(`ps -p ${pid}`);
                    return true;
                } catch {
                    return false;
                }
            }
        } catch (error) {
            console.warn(chalk.yellow(`⚠️ 检查进程状态失败: ${error.message}`));
            // 如果无法检查，假设进程正在运行（基于状态文件的最后更新时间）
            return true;
        }
    }

    /**
     * 格式化持续时间
     */
    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}天 ${hours % 24}小时 ${minutes % 60}分钟`;
        } else if (hours > 0) {
            return `${hours}小时 ${minutes % 60}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟 ${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }

    /**
     * 获取状态图标
     */
    getStatusIcon(status) {
        const icons = {
            'running': '🟢',
            'waiting': '🟡',
            'buying': '🔵',
            'error': '🔴',
            'stopped': '⚫'
        };
        return icons[status] || '⚪';
    }

    /**
     * 执行批量购买 - 将多个NFT打包到交易中
     */
    async executeBatchBuy(nfts, project, options = {}) {
        const { maxNFTsPerTx = 3,maxRetries=2, batchMode = 'sequential' } = options;

        console.log(chalk.blue.bold('\n🚀 开始批量购买...'));

        // 准备NFT数据
        const nftList = nfts.map(nft => ({
            creator_pubkey: project.creator_pubkey,
            project_pubkey: project.project_pubkey,
            nft_id: nft.nft_id,
            mint_pubkey: nft.mint_pubkey,
            nft_pubkey: nft.nft_pubkey,
            owner_pubkey: nft.owner_pubkey,
            price: nft.price
        }));

        try {
            // 使用PPP Buy脚本的批量购买功能 - 打包交易
            const result = await this.buyer.pppBuyScript.executeBatchBuy(nftList, {
                maxNFTsPerTx: maxNFTsPerTx,
                maxRetries: maxRetries,
                batchMode: batchMode
            });

            // 保存批量购买记录
            const batchRecord = {
                timestamp: new Date().toISOString(),
                project: project,
                nfts: nfts,
                batchConfig: { maxNFTsPerTx },
                result: result,
                buyer: this.buyer.walletManager.getAddress(),
                type: 'packed_transaction' // 标记为打包交易
            };

            // 创建文件名
            const now = new Date();
            const dateStr = now.toISOString().split('T')[0];
            const timeStr = now.toISOString().split('T')[1].replace(/:/g, '-').split('.')[0];
            const projectName = project.project_name || 'unknown';

            const filename = `batch-purchase-${dateStr}-${timeStr}-${projectName}.json`;
            this.buyer.saveData(batchRecord, filename);

            console.log(chalk.green.bold('\n✅ 批量购买完成!'));
            console.log(`📁 记录已保存到: ${filename}`);

        } catch (error) {
            console.log(chalk.red(`❌ 批量购买失败: ${error.message}`));
        }
    }

    /**
     * 钱包管理菜单
     */
    async walletMenu() {
        console.log(chalk.blue.bold('\n🔑 钱包管理\n'));

        // 检查是否有保存的钱包
        const hasSaved = this.buyer.walletManager.hasSavedWallet();
        const savedInfo = this.buyer.walletManager.getSavedWalletInfo();

        if (hasSaved && savedInfo) {
            console.log(chalk.green('💾 发现已保存的钱包:'));
            console.log(chalk.gray(`   地址: ${savedInfo.address}`));
            console.log(chalk.gray(`   保存时间: ${new Date(savedInfo.timestamp).toLocaleString()}`));
            console.log(chalk.gray(`   网络: ${savedInfo.network}\n`));
        }

        const choices = [
            { name: '📁 从文件加载钱包', value: 'file' },
            { name: '🔤 从私钥加载钱包', value: 'privatekey' },
            { name: '📊 查看钱包信息', value: 'info' }
        ];

        if (hasSaved) {
            choices.unshift({ name: '🔄 重新加载保存的钱包', value: 'reload' });
            choices.push({ name: '🗑️  清除保存的钱包', value: 'clear' });
        }

        choices.push({ name: '🔙 返回主菜单', value: 'back' });

        const { walletAction } = await inquirer.prompt([
            {
                type: 'list',
                name: 'walletAction',
                message: '选择钱包操作:',
                choices: choices
            }
        ]);

        switch (walletAction) {
            case 'reload':
                await this.reloadSavedWallet();
                break;
            case 'file':
                await this.loadWalletFromFile();
                break;
            case 'privatekey':
                await this.loadWalletFromPrivateKey();
                break;
            case 'info':
                await this.showWalletInfo();
                break;
            case 'clear':
                await this.clearSavedWallet();
                break;
            case 'back':
                return;
        }
    }

    /**
     * 重新加载保存的钱包
     */
    async reloadSavedWallet() {
        try {
            const success = this.buyer.walletManager.autoLoadWallet();
            if (success) {
                // 确保PPP Buy脚本也被重新初始化
                this.buyer.ensurePPPBuyScriptInitialized();
                console.log(chalk.green('✅ 保存的钱包重新加载成功!'));
                await this.showWalletInfo();
            } else {
                console.log(chalk.yellow('⚠️  重新加载失败，请手动加载钱包'));
            }
        } catch (error) {
            console.error(chalk.red(`❌ 重新加载失败: ${error.message}`));
        }
    }

    /**
     * 从文件加载钱包
     */
    async loadWalletFromFile() {
        const { filePath, saveWallet } = await inquirer.prompt([
            {
                type: 'input',
                name: 'filePath',
                message: '输入钱包文件路径:',
                default: './wallet.json',
                validate: (value) => {
                    if (!fs.existsSync(value)) {
                        return '文件不存在，请检查路径';
                    }
                    return true;
                }
            },
            {
                type: 'confirm',
                name: 'saveWallet',
                message: '是否保存钱包配置以便下次自动加载?',
                default: true
            }
        ]);

        try {
            const secretKey = JSON.parse(fs.readFileSync(filePath, 'utf8'));

            if (saveWallet) {
                const success = this.buyer.walletManager.initFromPrivateKeyAndSave(secretKey);
                if (success) {
                    this.buyer.ensurePPPBuyScriptInitialized();
                    console.log(chalk.green('✅ 钱包加载并保存成功!'));
                }
            } else {
                const success = this.buyer.walletManager.initFromPrivateKey(secretKey);
                if (success) {
                    this.buyer.ensurePPPBuyScriptInitialized();
                    console.log(chalk.green('✅ 钱包加载成功! (未保存)'));
                }
            }

            await this.showWalletInfo();
        } catch (error) {
            console.error(chalk.red(`❌ 钱包加载失败: ${error.message}`));
        }
    }

    /**
     * 从私钥加载钱包
     */
    async loadWalletFromPrivateKey() {
        const { privateKey, saveWallet } = await inquirer.prompt([
            {
                type: 'password',
                name: 'privateKey',
                message: '输入私钥 (支持Base58、JSON数组、逗号分隔格式):',
                mask: '*',
                validate: (value) => value.length > 0 || '请输入私钥'
            },
            {
                type: 'confirm',
                name: 'saveWallet',
                message: '是否保存钱包配置以便下次自动加载?',
                default: true
            }
        ]);

        try {
            if (saveWallet) {
                const success = this.buyer.walletManager.initFromPrivateKeyAndSave(privateKey);
                if (success) {
                    this.buyer.ensurePPPBuyScriptInitialized();
                    console.log(chalk.green('✅ 钱包初始化并保存成功!'));
                }
            } else {
                const success = this.buyer.walletManager.initFromPrivateKey(privateKey);
                if (success) {
                    this.buyer.ensurePPPBuyScriptInitialized();
                    console.log(chalk.green('✅ 钱包初始化成功! (未保存)'));
                }
            }

            await this.showWalletInfo();
        } catch (error) {
            console.error(chalk.red(`❌ 钱包初始化失败: ${error.message}`));
        }
    }

    /**
     * 清除保存的钱包
     */
    async clearSavedWallet() {
        const { confirm } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: '确认清除保存的钱包配置? 这将删除本地保存的钱包信息',
                default: false
            }
        ]);

        if (confirm) {
            const success = this.buyer.walletManager.clearSavedWallet();
            if (success) {
                console.log(chalk.green('✅ 钱包配置已清除'));
            }
        } else {
            console.log(chalk.yellow('❌ 操作已取消'));
        }
    }

    /**
     * 显示钱包信息
     */
    async showWalletInfo() {
        console.log(chalk.blue.bold('\n📊 钱包信息\n'));

        const walletInfo = await this.buyer.getWalletInfo();
        const hasSaved = this.buyer.walletManager.hasSavedWallet();
        const savedInfo = this.buyer.walletManager.getSavedWalletInfo();

        if (!walletInfo) {
            console.log(chalk.yellow('⚠️  当前钱包未初始化'));

            if (hasSaved && savedInfo) {
                console.log(chalk.blue('\n💾 发现保存的钱包配置:'));
                console.log(chalk.gray(`   地址: ${savedInfo.address}`));
                console.log(chalk.gray(`   保存时间: ${new Date(savedInfo.timestamp).toLocaleString()}`));
                console.log(chalk.gray(`   网络: ${savedInfo.network}`));
                console.log(chalk.blue('   提示: 可以在钱包管理中重新加载'));
            }
            return;
        }

        console.log(chalk.green(`当前地址: ${walletInfo.address}`));
        console.log(chalk.green(`SOL余额: ${walletInfo.solBalance.toFixed(6)} SOL`));

        if (walletInfo.solBalance < this.config.MIN_SOL_BALANCE * 10) {
            console.log(chalk.red('⚠️  余额较低，建议充值'));
        } else {
            console.log(chalk.green('✅ 余额充足'));
        }

        // 显示保存状态
        if (hasSaved && savedInfo) {
            if (savedInfo.address === walletInfo.address) {
                console.log(chalk.blue('💾 此钱包已保存，下次启动将自动加载'));
            } else {
                console.log(chalk.yellow('⚠️  当前钱包与保存的钱包不同'));
                console.log(chalk.gray(`   保存的地址: ${savedInfo.address}`));
            }
        } else {
            console.log(chalk.gray('📝 此钱包未保存，退出后需要重新加载'));
        }
    }


    /**
     * 选择项目
     */
    async selectProject() {
        const { method } = await inquirer.prompt([
            {
                type: 'list',
                name: 'method',
                message: '如何选择项目:',
                choices: [
                    { name: '🔍 搜索项目', value: 'search' },
                    { name: '📋 浏览热门项目', value: 'hot' },
                    { name: '🎯 直接输入项目地址', value: 'direct' },
                    { name: '📝 列出所有项目mint地址', value: 'list_all' }
                ]
            }
        ]);

        let projects = [];
        let selectedProject = null;

        switch (method) {
            case 'search':
                const { keyword } = await inquirer.prompt([
                    {
                        type: 'input',
                        name: 'keyword',
                        message: '输入搜索关键词:',
                        validate: (value) => value.length > 0 || '请输入关键词'
                    }
                ]);

                const searchResult = await this.buyer.getProjects({ keyword, limit: 10 });
                projects = searchResult.data || [];
                break;

            case 'hot':
                const hotResult = await this.buyer.getProjects({ limit: 20, sort: 'volume_24h_desc' });
                projects = hotResult.data || [];
                break;

            case 'direct':
                const { projectMint } = await inquirer.prompt([
                    {
                        type: 'input',
                        name: 'projectMint',
                        message: '输入项目mint地址:',
                        validate: (value) => value.length > 0 || '请输入项目地址'
                    }
                ]);
                return { project_mint: projectMint, project_name: 'Direct Input' };

            case 'list_all':
                return await this.selectProjectFromListWithBatch();
        }

        if (projects.length === 0) {
            console.log(chalk.yellow('未找到项目'));
            return null;
        }

        const { projectChoice } = await inquirer.prompt([
            {
                type: 'list',
                name: 'projectChoice',
                message: '选择项目:',
                choices: projects.map((project, index) => ({
                    name: `${project.project_name} (${project.token_symbol}) - ${project.nft_issue_count - project.nft_burn_count} 活跃NFT`,
                    value: index
                }))
            }
        ]);

        selectedProject = projects[projectChoice];
        this.buyer.displayProject(selectedProject, 0);

        return selectedProject;
    }

    /**
     * 从所有项目列表中选择项目并批量购买
     */
    async selectProjectFromList() {
        console.log(chalk.blue('\n📋 获取所有项目列表...'));

        try {
            // 使用与浏览项目相同的参数来避免400错误
            const allProjectsResult = await this.buyer.getProjects({ limit: 50, sort: '' });
            const allProjects = allProjectsResult.data || [];

            if (allProjects.length === 0) {
                console.log(chalk.yellow('未找到任何项目'));
                return null;
            }

            // 过滤出有有效mint地址的项目
            const validProjects = allProjects.filter(project =>
                project.project_mint &&
                project.project_mint !== 'null' &&
                project.project_mint.length > 20 // 确保是有效的Solana地址
            );

            if (validProjects.length === 0) {
                console.log(chalk.yellow('未找到有有效mint地址的项目'));
                console.log(chalk.gray('提示: 请使用其他方式选择项目'));
                return null;
            }

            console.log(chalk.green(`\n找到 ${validProjects.length} 个有效项目 (共${allProjects.length}个项目)`));

            // 选择项目
            const { projectChoice } = await inquirer.prompt([
                {
                    type: 'list',
                    name: 'projectChoice',
                    message: '选择项目:',
                    choices: [
                        ...validProjects.map((project, index) => ({
                            name: `${project.project_name} (${project.token_symbol}) - Mint: ${project.project_mint.substring(0, 8)}... - ${project.nft_issue_count - project.nft_burn_count} 活跃NFT`,
                            value: index
                        })),
                        { name: '❌ 取消', value: -1 }
                    ]
                }
            ]);

            if (projectChoice === -1) {
                return null;
            }

            const selectedProject = validProjects[projectChoice];

            // 显示项目信息
            console.log(chalk.blue.bold(`\n📊 项目信息:`));
            console.log(`项目名称: ${selectedProject.project_name}`);
            console.log(`代币符号: ${selectedProject.token_symbol}`);
            console.log(`Mint地址: ${selectedProject.project_mint}`);
            console.log(`活跃NFT数量: ${selectedProject.nft_issue_count - selectedProject.nft_burn_count}`);
            console.log(`24h交易量: $${this.buyer.formatNumber(selectedProject.volume_24h)}`);

            // 输入购买数量
            const { buyCount } = await inquirer.prompt([
                {
                    type: 'number',
                    name: 'buyCount',
                    message: '输入要购买的NFT数量:',
                    default: 1,
                    validate: (value) => {
                        if (value <= 0) return '购买数量必须大于0';
                        if (value > 50) return '单次最多购买50个NFT';
                        if (value > (selectedProject.nft_issue_count - selectedProject.nft_burn_count)) {
                            return `购买数量不能超过活跃NFT数量 (${selectedProject.nft_issue_count - selectedProject.nft_burn_count})`;
                        }
                        return true;
                    }
                }
            ]);

            // 确认批量购买
            const { confirmBatch } = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'confirmBatch',
                    message: `确认从项目 ${selectedProject.project_name} 购买价格最高的 ${buyCount} 个NFT?`,
                    default: false
                }
            ]);

            if (!confirmBatch) {
                console.log(chalk.yellow('❌ 批量购买已取消'));
                return null;
            }

            // 执行批量购买
            await this.executeBatchPurchase(selectedProject, buyCount);
            return selectedProject;

        } catch (error) {
            console.error(chalk.red(`获取项目列表失败: ${error.message}`));
            console.log(chalk.yellow('\n💡 建议使用其他方式选择项目:'));
            console.log(chalk.gray('  1. 🔍 搜索项目'));
            console.log(chalk.gray('  2. 📋 浏览热门项目'));
            console.log(chalk.gray('  3. 🎯 直接输入项目地址'));

            // 提供备用选择
            const { fallback } = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'fallback',
                    message: '是否使用浏览热门项目作为备用方案?',
                    default: true
                }
            ]);

            if (fallback) {
                console.log(chalk.blue('\n📋 切换到浏览热门项目...'));
                try {
                    const hotResult = await this.buyer.getProjects({ limit: 20, sort: '' });
                    const hotProjects = hotResult.data || [];

                    if (hotProjects.length > 0) {
                        const validHotProjects = hotProjects.filter(project =>
                            project.project_mint &&
                            project.project_mint !== 'null' &&
                            project.project_mint.length > 20
                        );

                        if (validHotProjects.length > 0) {
                            console.log(chalk.green(`找到 ${validHotProjects.length} 个有效项目`));

                            const { projectChoice } = await inquirer.prompt([
                                {
                                    type: 'list',
                                    name: 'projectChoice',
                                    message: '选择项目:',
                                    choices: [
                                        ...validHotProjects.slice(0, 10).map((project, index) => ({
                                            name: `${project.project_name} (${project.token_symbol}) - Mint: ${project.project_mint.substring(0, 8)}...`,
                                            value: index
                                        })),
                                        { name: '❌ 取消', value: -1 }
                                    ]
                                }
                            ]);

                            if (projectChoice !== -1) {
                                const selectedProject = validHotProjects[projectChoice];
                                console.log(chalk.blue.bold(`\n📊 项目信息:`));
                                console.log(`项目名称: ${selectedProject.project_name}`);
                                console.log(`代币符号: ${selectedProject.token_symbol}`);
                                console.log(`Mint地址: ${selectedProject.project_mint}`);

                                return selectedProject;
                            }
                        }
                    }
                } catch (fallbackError) {
                    console.error(chalk.red(`备用方案也失败了: ${fallbackError.message}`));
                }
            }

            return null;
        }
    }

    /**
     * 检查项目是否支持购买
     */
    isProjectSupported(mintAddress) {
        const supportedProjects = [
            'iNxCAjhbc4L5jVEe3ajHdY28rqcNBGPpqMVkiEZZppp', // Rich
            '63B7LWnQDwAPybnjpiLrEEx1cVGG9jwxmQBc3BAMWppp', // CAI
            'EPmFmNuDgqniPgn7dV2da3EEamEGj1smyQ7PXDWHoppp',  // DOIT
            '5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp'  // Ani
        ];
        return supportedProjects.includes(mintAddress);
    }

    /**
     * 获取支持的项目列表
     */
    getSupportedProjectsInfo() {
        return [
            { mint: 'iNxCAjhbc4L5jVEe3ajHdY28rqcNBGPpqMVkiEZZppp', name: 'Rich', symbol: 'RICH' },
            { mint: '63B7LWnQDwAPybnjpiLrEEx1cVGG9jwxmQBc3BAMWppp', name: 'CAI', symbol: 'CAI' },
            { mint: 'EPmFmNuDgqniPgn7dV2da3EEamEGj1smyQ7PXDWHoppp', name: 'Just do it', symbol: 'DOIT' },
            { mint: '5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp', name: 'Ani Grok Companion', symbol: 'Ani' }
        ];
    }

    /**
     * 简化版项目选择（仅选择项目，不执行购买）
     */
    async selectProjectFromListWithBatch() {
        console.log(chalk.blue('\n📋 获取项目列表...'));

        try {
            // 使用与浏览项目完全相同的参数
            const result = await this.buyer.getProjects({ limit: 20, sort: '' });
            const projects = result.data || [];

            if (projects.length === 0) {
                console.log(chalk.yellow('未找到任何项目'));
                return null;
            }

            // 过滤出有有效mint地址的项目
            const validProjects = projects.filter(project => {
                return project.project_mint &&
                       project.project_mint !== 'null' &&
                       project.project_mint !== null &&
                       project.project_mint.length > 20;
            });

            console.log(chalk.blue(`\n📊 项目统计:`));
            console.log(`总项目数: ${projects.length}`);
            console.log(`有效mint地址的项目: ${validProjects.length}`);

            if (validProjects.length === 0) {
                console.log(chalk.yellow('\n⚠️ 没有找到有有效mint地址的项目'));
                console.log(chalk.gray('当前可用的项目:'));

                projects.slice(0, 5).forEach((project, index) => {
                    console.log(chalk.gray(`${index + 1}. ${project.project_name} (${project.token_symbol}) - Mint: ${project.project_mint || 'null'}`));
                });

                console.log(chalk.yellow('\n💡 建议使用 "🎯 直接输入项目地址" 选项'));
                return null;
            }

            // 显示有效项目列表
            console.log(chalk.green.bold('\n📝 有效项目列表 (有mint地址):'));
            validProjects.forEach((project, index) => {
                const activeNFTs = (project.nft_issue_count || 0) - (project.nft_burn_count || 0);
                const isSupported = this.isProjectSupported(project.project_mint);
                const supportStatus = isSupported ? chalk.green('✅ 支持') : chalk.red('❌ 不支持');

                console.log(chalk.cyan(`${index + 1}. ${project.project_name} (${project.token_symbol}) ${supportStatus}`));
                console.log(chalk.gray(`   Mint: ${project.project_mint}`));
                console.log(chalk.gray(`   活跃NFT: ${activeNFTs}`));
                console.log('');
            });

            // 选择项目
            const { projectChoice } = await inquirer.prompt([
                {
                    type: 'list',
                    name: 'projectChoice',
                    message: '选择要购买的项目:',
                    choices: [
                        ...validProjects.map((project, index) => {
                            const activeNFTs = (project.nft_issue_count || 0) - (project.nft_burn_count || 0);
                            const isSupported = this.isProjectSupported(project.project_mint);
                            const supportIcon = isSupported ? '✅' : '❌';
                            return {
                                name: `${supportIcon} ${project.project_name} (${project.token_symbol}) - ${activeNFTs} 活跃NFT`,
                                value: index
                            };
                        }),
                        { name: '❌ 取消', value: -1 }
                    ]
                }
            ]);

            if (projectChoice === -1) {
                return null;
            }

            const selectedProject = validProjects[projectChoice];

            // 检查项目支持状态
            const isSupported = this.isProjectSupported(selectedProject.project_mint);

            // 显示选中项目的详细信息
            console.log(chalk.blue.bold(`\n📊 选中项目详情:`));
            console.log(`项目名称: ${selectedProject.project_name}`);
            console.log(`代币符号: ${selectedProject.token_symbol}`);
            console.log(`Mint地址: ${selectedProject.project_mint}`);
            console.log(`已发行NFT: ${selectedProject.nft_issue_count || 0}`);
            console.log(`已销毁NFT: ${selectedProject.nft_burn_count || 0}`);
            console.log(`活跃NFT: ${(selectedProject.nft_issue_count || 0) - (selectedProject.nft_burn_count || 0)}`);

            // 显示支持状态
            if (isSupported) {
                console.log(chalk.green(`购买支持: ✅ 支持批量购买`));
            } else {
                console.log(chalk.red(`购买支持: ❌ 暂不支持`));
                console.log(chalk.yellow('\n⚠️ 该项目暂不支持批量购买功能'));

                const supportedProjects = this.getSupportedProjectsInfo();
                console.log(chalk.blue('\n🎯 当前支持的项目:'));
                supportedProjects.forEach(project => {
                    console.log(chalk.gray(`  • ${project.name} (${project.symbol})`));
                });

                const { continueAnyway } = await inquirer.prompt([
                    {
                        type: 'confirm',
                        name: 'continueAnyway',
                        message: '是否仍要尝试购买? (可能会失败)',
                        default: false
                    }
                ]);

                if (!continueAnyway) {
                    console.log(chalk.yellow('❌ 已取消购买'));
                    return null;
                }
            }

            const activeNFTCount = (selectedProject.nft_issue_count || 0) - (selectedProject.nft_burn_count || 0);

            if (activeNFTCount <= 0) {
                console.log(chalk.yellow('\n⚠️ 该项目没有活跃的NFT可供购买'));
                return null;
            }

            // 只返回选中的项目，不执行购买逻辑
            return selectedProject;

        } catch (error) {
            console.error(chalk.red(`获取项目列表失败: ${error.message}`));
            console.log(chalk.yellow('\n💡 请尝试使用其他项目选择方式'));
            return null;
        }
    }

    /**
     * 选择NFT
     */
    async selectNFT(projectMint) {
        console.log(chalk.blue('\n🎨 获取NFT列表...'));

        const nftResult = await this.buyer.getProjectNFTs(projectMint, { 
            limit: 50, 
            sort: 'price', 
            order: 'asc' 
        });

        if (!nftResult.data || nftResult.data.length === 0) {
            console.log(chalk.yellow('该项目暂无NFT'));
            return null;
        }

        const activeNFTs = nftResult.data.filter(nft => nft.is_burned === 0);
        
        if (activeNFTs.length === 0) {
            console.log(chalk.yellow('没有活跃的NFT可供购买'));
            return null;
        }

        console.log(chalk.green(`\n找到 ${activeNFTs.length} 个活跃NFT`));

        const { nftChoice } = await inquirer.prompt([
            {
                type: 'list',
                name: 'nftChoice',
                message: '选择要购买的NFT:',
                choices: [
                    ...activeNFTs.slice(0, 20).map((nft, index) => ({
                        name: `NFT ${nft.nft_id} - ${(nft.price / 1000000000).toFixed(6)} SOL (轮次: ${nft.round})`,
                        value: index
                    })),
                    { name: '❌ 取消', value: -1 }
                ]
            }
        ]);

        if (nftChoice === -1) {
            return null;
        }

        const selectedNFT = activeNFTs[nftChoice];
        this.buyer.displayNFT(selectedNFT, 0);
        
        return selectedNFT;
    }

    /**
     * 执行购买
     */
    async executePurchase(nft, project) {
        try {
            console.log(chalk.blue.bold('\n🛒 准备购买...'));

            // 检查购买条件
            await this.buyer.checkBuyConditions(nft);

            const priceSol = nft.price / 1000000000;
            const { confirm } = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'confirm',
                    message: `确认购买NFT ${nft.nft_id} (${priceSol.toFixed(6)} SOL)?`,
                    default: false
                }
            ]);

            if (!confirm) {
                console.log(chalk.yellow('❌ 购买已取消'));
                return;
            }

            console.log(chalk.blue('🔄 执行购买交易...'));
            const result = await this.buyer.buyNFT(nft, project.project_mint);

            if (result) {
                // 保存购买记录
                const purchaseRecord = {
                    timestamp: new Date().toISOString(),
                    project: project,
                    nft: nft,
                    transaction: result,
                    buyer: this.buyer.walletManager.getAddress()
                };

                // 创建带日期和项目名的文件名
                const now = new Date();
                const dateStr = now.toISOString().split('T')[0];
                const timeStr = now.toISOString().split('T')[1].replace(/:/g, '-').split('.')[0];
                const projectName = project.project_name || 'unknown';

                const filename = `single-purchase-${dateStr}-${timeStr}-${projectName}-NFT${nft.nft_id}.json`;
                this.buyer.saveData(purchaseRecord, filename);

                console.log(chalk.green.bold('\n🎉 购买成功!'));
                console.log(chalk.blue(`查看交易: https://solscan.io/tx/${result.signature}`));
            }

        } catch (error) {
            console.error(chalk.red(`❌ 购买失败: ${error.message}`));
        }
    }

    /**
     * 执行批量购买
     */
    async executeBatchPurchase(project, buyCount, options = {}) {
        const { concurrency = 3, maxRetries = 3 } = options;
        try {
            console.log(chalk.blue.bold(`\n🛒 开始批量购买 ${buyCount} 个NFT...`));

            // 获取项目的所有活跃NFT
            console.log(chalk.blue('📋 获取项目NFT列表...'));
            const nftResult = await this.buyer.getProjectNFTs(project.project_mint, {
                limit: 200,
                sort: 'price',
                order: 'asc'
            });

            if (!nftResult.data || nftResult.data.length === 0) {
                console.log(chalk.yellow('该项目暂无NFT'));
                return;
            }

            const activeNFTs = nftResult.data.filter(nft => nft.is_burned === 0);

            if (activeNFTs.length === 0) {
                console.log(chalk.yellow('没有活跃的NFT可供购买'));
                return;
            }

            if (activeNFTs.length < buyCount) {
                console.log(chalk.yellow(`活跃NFT数量 (${activeNFTs.length}) 少于购买数量 (${buyCount})`));
                buyCount = activeNFTs.length;
            }

            // 按价格从高到低排序选择NFT
            console.log(chalk.blue(`💰 从 ${activeNFTs.length} 个活跃NFT中选择价格最高的 ${buyCount} 个...`));
            const sortedNFTs = [...activeNFTs].sort((a, b) => b.price - a.price);
            let selectedNFTs = sortedNFTs.slice(0, buyCount);

            // 过滤掉自己的NFT  自己的不能购买
           const myAddress = this.buyer.walletManager.getAddress();
           selectedNFTs = selectedNFTs.filter(nft => nft.owner_pubkey !== myAddress);

            // 显示选中的NFT
            console.log(chalk.green.bold('\n📋 选中的NFT列表:'));
            let totalPrice = 0;
            selectedNFTs.forEach((nft, index) => {
                const priceSol = nft.price / 1000000000;
                totalPrice += priceSol;
                console.log(`${index + 1}. NFT ${nft.nft_id} - ${priceSol.toFixed(6)} SOL (轮次: ${nft.round})`);
                console.log(JSON.stringify(nft));
            });



            console.log(chalk.blue.bold(`\n💰 总价格: ${totalPrice.toFixed(6)} SOL`));
            console.log(chalk.green.bold(`\n🚀 开始购买流程...`));

            // 并发购买NFT
            let successCount = 0;
            let failCount = 0;
            const results = [];

            console.log(chalk.blue.bold(`\n🚀 开始并发购买 (并发数: ${concurrency}, 最大重试: ${maxRetries} 次)...`));

            // 创建购买任务
            const purchaseTasks = selectedNFTs.map((nft, index) => ({
                nft,
                index,
                retries: 0,
                completed: false
            }));

            // 并发购买函数
            const purchaseNFT = async (task) => {
                const { nft } = task;
                const priceSol = nft.price / 1000000000;

                while (task.retries < maxRetries && !task.completed) {
                    const attemptNum = task.retries + 1;
                    console.log(chalk.blue.bold(`\n🛒 购买NFT ${nft.nft_id} (第${attemptNum}次尝试)...`));
                    console.log(`价格: ${priceSol.toFixed(6)} SOL`);

                    try {
                        // 检查购买条件
                        await this.buyer.checkBuyConditions(nft);

                        // 执行购买
                        const buyParams = {
                            mintpubkey: nft.mint_pubkey,
                            nftPubkey: nft.nft_pubkey,
                            previousOwner: nft.owner_pubkey
                        };

                        // 直接调用PPP Buy脚本
                        const result = await this.buyer.pppBuyScript.executeBuy(buyParams);

                        if (result && result.success) {
                            task.completed = true;
                            successCount++;
                            console.log(chalk.green(`✅ NFT ${nft.nft_id} 购买成功! (第${attemptNum}次尝试)`));
                            console.log(chalk.blue(`交易签名: ${result.signature}`));

                            results.push({
                                nft_id: nft.nft_id,
                                success: true,
                                signature: result.signature,
                                price: priceSol,
                                attempts: attemptNum
                            });

                            // 保存购买记录
                            const purchaseRecord = {
                                timestamp: new Date().toISOString(),
                                project: project,
                                nft: nft,
                                transaction: result,
                                buyer: this.buyer.walletManager.getAddress(),
                                attempts: attemptNum
                            };

                            // 创建带日期和项目名的文件名
                            const now = new Date();
                            const dateStr = now.toISOString().split('T')[0];
                            const timeStr = now.toISOString().split('T')[1].replace(/:/g, '-').split('.')[0];
                            const projectName = project.project_name || 'unknown';

                            const filename = `nft-purchase-${dateStr}-${timeStr}-${projectName}-NFT${nft.nft_id}.json`;
                            this.buyer.saveData(purchaseRecord, filename);

                            return; // 成功后退出重试循环
                        } else {
                            throw new Error('购买失败，未返回成功结果');
                        }

                    } catch (error) {
                        // 检查是否是不支持的项目错误
                        if (error.message.includes('不支持的项目')) {
                            task.completed = true;
                            failCount++;
                            console.error(chalk.red(`❌ NFT ${nft.nft_id} 购买失败:`));
                            console.error(chalk.yellow(error.message));

                            results.push({
                                nft_id: nft.nft_id,
                                success: false,
                                error: '项目不支持',
                                price: priceSol,
                                attempts: attemptNum
                            });
                            return; // 项目不支持时不重试
                        }

                        // 检查是否是NFT已被购买的错误
                        const isOwnerMismatch = error.message.includes('Owner account mismatch');
                        const isAlreadySold = error.message.includes('已被购买') || error.message.includes('not available');

                        if (isOwnerMismatch || isAlreadySold) {
                            task.completed = true;
                            failCount++;
                            console.log(chalk.yellow(`⚠️ NFT ${nft.nft_id} 已被他人购买，跳过重试`));

                            results.push({
                                nft_id: nft.nft_id,
                                success: false,
                                error: 'NFT已被他人购买',
                                price: priceSol,
                                attempts: attemptNum,
                                skipRetry: true
                            });
                            return; // NFT已售出时不重试
                        }

                        task.retries++;
                        const isLastAttempt = task.retries >= maxRetries;

                        if (isLastAttempt) {
                            task.completed = true;
                            failCount++;
                            console.error(chalk.red(`❌ NFT ${nft.nft_id} 最终购买失败 (尝试${maxRetries}次): ${error.message}`));

                            results.push({
                                nft_id: nft.nft_id,
                                success: false,
                                error: error.message,
                                price: priceSol,
                                attempts: maxRetries
                            });
                        } else {
                            console.log(chalk.yellow(`⚠️ NFT ${nft.nft_id} 第${attemptNum}次尝试失败: ${error.message}`));
                            console.log(chalk.gray(`⏳ 等待 2 秒后重试...`));
                            await new Promise(resolve => setTimeout(resolve, 2000));
                        }
                    }
                }
            };

            // 分批并发执行
            for (let i = 0; i < purchaseTasks.length; i += concurrency) {
                const batch = purchaseTasks.slice(i, i + concurrency);
                console.log(chalk.cyan(`\n📦 处理批次 ${Math.floor(i / concurrency) + 1} (${batch.length} 个NFT)...`));

                // 并发执行当前批次
                await Promise.all(batch.map(task => purchaseNFT(task)));

                // 批次间延迟
                if (i + concurrency < purchaseTasks.length) {
                    console.log(chalk.gray('⏳ 批次间等待 1 秒...'));
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            // 显示批量购买结果
            console.log(chalk.blue.bold('\n📊 批量购买结果:'));
            console.log(chalk.green(`✅ 成功: ${successCount} 个`));
            console.log(chalk.red(`❌ 失败: ${failCount} 个`));

            const successfulPurchases = results.filter(r => r.success);
            const totalSpent = successfulPurchases.reduce((sum, r) => sum + r.price, 0);
            console.log(chalk.blue(`💰 总花费: ${totalSpent.toFixed(6)} SOL`));

            // 保存批量购买汇总
            const batchSummary = {
                timestamp: new Date().toISOString(),
                project: project,
                total_count: buyCount,
                success_count: successCount,
                fail_count: failCount,
                total_spent: totalSpent,
                results: results,
                buyer: this.buyer.walletManager.getAddress()
            };

            // 创建带日期的文件名
            const now = new Date();
            const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
            const timeStr = now.toISOString().split('T')[1].replace(/:/g, '-').split('.')[0]; // HH-MM-SS

            const summaryFilename = `batch-purchase-summary-${dateStr}-${timeStr}-${project.project_name || 'unknown'}.json`;
            this.buyer.saveData(batchSummary, summaryFilename);

            if (successCount > 0) {
                console.log(chalk.green.bold('\n🎉 批量购买完成!'));
                console.log(chalk.blue(`详细记录已保存到: ${summaryFilename}`));
            }

        } catch (error) {
            console.error(chalk.red(`❌ 批量购买失败: ${error.message}`));
        }
    }

    /**
     * 项目菜单
     */
    async projectsMenu() {
        console.log(chalk.blue.bold('\n📋 项目浏览\n'));
        
        const result = await this.buyer.getProjects({ limit: 10 });
        if (result && result.data) {
            result.data.forEach((project, index) => {
                this.buyer.displayProject(project, index);
            });
        }
    }

    /**
     * NFT菜单
     */
    async nftsMenu() {
        console.log(chalk.blue.bold('\n🎨 NFT查看\n'));
        
        const { projectMint } = await inquirer.prompt([
            {
                type: 'input',
                name: 'projectMint',
                message: '输入项目mint地址:',
                validate: (value) => value.length > 0 || '请输入项目地址'
            }
        ]);

        const result = await this.buyer.getProjectNFTs(projectMint, { limit: 10 });
        if (result && result.data) {
            result.data.forEach((nft, index) => {
                this.buyer.displayNFT(nft, index);
            });
        }
    }

    /**
     * 设置菜单
     */
    async settingsMenu() {
        console.log(chalk.blue.bold('\n⚙️  设置\n'));
        
        const { setting } = await inquirer.prompt([
            {
                type: 'list',
                name: 'setting',
                message: '选择设置项:',
                choices: [
                    { name: '🌐 切换网络', value: 'network' },
                    { name: '🔙 返回主菜单', value: 'back' }
                ]
            }
        ]);

        if (setting === 'network') {
            const { network } = await inquirer.prompt([
                {
                    type: 'list',
                    name: 'network',
                    message: '选择网络:',
                    choices: [
                        { name: '🌐 主网 (Mainnet)', value: 'mainnet' },
                        { name: '🧪 测试网 (Devnet)', value: 'devnet' }
                    ]
                }
            ]);

            this.config = getConfig(network);
            console.log(chalk.green(`✅ 已切换到 ${network}`));
        }
    }
}

// 主函数
async function main() {
    const enhancedBuyer = new EnhancedNFTBuyer();
    
    try {
        console.log(chalk.blue('🚀 启动增强版NFT购买工具...'));

        // 初始化连接
        console.log(chalk.blue('📡 正在初始化连接...'));
        await enhancedBuyer.buyer.initialize();
        console.log(chalk.green('✅ 初始化完成'));

        // 显示主菜单
        console.log(chalk.blue('📋 显示主菜单...'));
        await enhancedBuyer.showMainMenu();
    } catch (error) {
        console.error(chalk.red(`程序启动失败: ${error.message}`));
        console.error(error.stack);
        process.exit(1);
    }
}

// 错误处理
process.on('uncaughtException', (error) => {
    console.log(chalk.red(`❌ 未捕获的异常: ${error.message}`));
    process.exit(1);
});

process.on('unhandledRejection', (reason) => {
    console.log(chalk.red(`❌ 未处理的Promise拒绝: ${reason}`));
    process.exit(1);
});

// 启动程序
main().catch(console.error);

export { EnhancedNFTBuyer };
