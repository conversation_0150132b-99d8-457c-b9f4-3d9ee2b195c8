#!/usr/bin/env node

import { PPPProjectNFTBuyer } from './ppp-project-nft-buyer.js';
import { CONFIG } from './config/config.js';
import chalk from 'chalk';
import fs from 'fs';

/**
 * PPP数据提供器
 * 用于获取和缓存项目列表和NFT数据
 */
class PPPDataProvider {
    constructor() {
        this.buyer = new PPPProjectNFTBuyer();
        this.projectsCache = null;
        this.nftsCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
    }

    /**
     * 初始化数据提供器
     */
    async initialize() {
        await this.buyer.initialize();
        console.log(chalk.green('✅ 数据提供器初始化完成'));
    }

    /**
     * 获取所有项目列表
     */
    async getAllProjects(options = {}) {
        const {
            limit = 100,
            useCache = true,
            sort = 'volume_24h_desc'
        } = options;

        // 检查缓存
        if (useCache && this.projectsCache && this.isValidCache(this.projectsCache.timestamp)) {
            console.log(chalk.blue('📋 使用缓存的项目数据'));
            return this.projectsCache.data;
        }

        console.log(chalk.blue('📋 获取最新项目数据...'));
        
        try {
            const result = await this.buyer.getProjects({ limit, sort });
            
            if (result && result.data) {
                // 缓存数据
                this.projectsCache = {
                    data: result.data,
                    timestamp: Date.now()
                };

                console.log(chalk.green(`✅ 成功获取 ${result.data.length} 个项目`));
                return result.data;
            }
            
            return [];
        } catch (error) {
            console.error(chalk.red(`获取项目失败: ${error.message}`));
            return [];
        }
    }

    /**
     * 获取热门项目
     */
    async getHotProjects(limit = 20) {
        console.log(chalk.blue('🔥 获取热门项目...'));
        return await this.getAllProjects({ 
            limit, 
            sort: 'volume_24h_desc' 
        });
    }

    /**
     * 获取最新项目
     */
    async getLatestProjects(limit = 20) {
        console.log(chalk.blue('🆕 获取最新项目...'));
        return await this.getAllProjects({ 
            limit, 
            sort: 'create_time_desc' 
        });
    }

    /**
     * 搜索项目
     */
    async searchProjects(keyword, limit = 20) {
        console.log(chalk.blue(`🔍 搜索项目: "${keyword}"`));
        
        try {
            const result = await this.buyer.getProjects({ keyword, limit });
            
            if (result && result.data) {
                console.log(chalk.green(`✅ 找到 ${result.data.length} 个相关项目`));
                return result.data;
            }
            
            return [];
        } catch (error) {
            console.error(chalk.red(`搜索失败: ${error.message}`));
            return [];
        }
    }

    /**
     * 获取项目的所有NFT
     */
    async getProjectNFTs(projectMint, options = {}) {
        const {
            limit = 100,
            useCache = true,
            sort = 'price',
            order = 'asc',
            activeOnly = true
        } = options;

        const cacheKey = `${projectMint}_${limit}_${sort}_${order}`;

        // 检查缓存
        if (useCache && this.nftsCache.has(cacheKey)) {
            const cached = this.nftsCache.get(cacheKey);
            if (this.isValidCache(cached.timestamp)) {
                console.log(chalk.blue('🎨 使用缓存的NFT数据'));
                return cached.data;
            }
        }

        console.log(chalk.blue(`🎨 获取项目 ${projectMint.substring(0, 8)}... 的NFT数据...`));
        
        try {
            const result = await this.buyer.getProjectNFTs(projectMint, { 
                limit, 
                sort, 
                order 
            });
            
            if (result && result.data) {
                let nfts = result.data;
                
                // 过滤活跃NFT
                if (activeOnly) {
                    nfts = nfts.filter(nft => nft.is_burned === 0);
                }

                // 缓存数据
                this.nftsCache.set(cacheKey, {
                    data: nfts,
                    timestamp: Date.now()
                });

                console.log(chalk.green(`✅ 成功获取 ${nfts.length} 个NFT`));
                return nfts;
            }
            
            return [];
        } catch (error) {
            console.error(chalk.red(`获取NFT失败: ${error.message}`));
            return [];
        }
    }

    /**
     * 获取项目的最便宜NFT
     */
    async getCheapestNFTs(projectMint, limit = 10) {
        console.log(chalk.blue('💰 获取最便宜的NFT...'));
        return await this.getProjectNFTs(projectMint, {
            limit,
            sort: 'price',
            order: 'asc'
        });
    }

    /**
     * 获取项目的最新交易NFT
     */
    async getRecentNFTs(projectMint, limit = 10) {
        console.log(chalk.blue('⏰ 获取最近交易的NFT...'));
        return await this.getProjectNFTs(projectMint, {
            limit,
            sort: 'last_trade',
            order: 'desc'
        });
    }

    /**
     * 获取特定NFT信息
     */
    async getNFTById(projectMint, nftId) {
        console.log(chalk.blue(`🎯 获取NFT ${nftId} 信息...`));
        
        try {
            const result = await this.buyer.getProjectNFTs(projectMint, { 
                nft_id: nftId, 
                limit: 1 
            });
            
            if (result && result.data && result.data.length > 0) {
                return result.data[0];
            }
            
            return null;
        } catch (error) {
            console.error(chalk.red(`获取NFT失败: ${error.message}`));
            return null;
        }
    }

    /**
     * 获取项目统计信息
     */
    async getProjectStats(projectMint) {
        console.log(chalk.blue('📊 计算项目统计信息...'));
        
        const nfts = await this.getProjectNFTs(projectMint, { 
            limit: 1000, 
            activeOnly: false 
        });

        if (nfts.length === 0) {
            return null;
        }

        const activeNFTs = nfts.filter(nft => nft.is_burned === 0);
        const burnedNFTs = nfts.filter(nft => nft.is_burned === 1);
        
        const prices = activeNFTs.map(nft => nft.price / **********);
        const totalValue = prices.reduce((sum, price) => sum + price, 0);
        
        return {
            total: nfts.length,
            active: activeNFTs.length,
            burned: burnedNFTs.length,
            totalValue: totalValue,
            averagePrice: prices.length > 0 ? totalValue / prices.length : 0,
            minPrice: prices.length > 0 ? Math.min(...prices) : 0,
            maxPrice: prices.length > 0 ? Math.max(...prices) : 0,
            totalSplits: nfts.reduce((sum, nft) => sum + nft.split_count, 0)
        };
    }

    /**
     * 保存项目数据到文件
     */
    async saveProjectsData(filename = null) {
        const projects = await this.getAllProjects();
        
        if (!filename) {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            filename = `projects-data-${timestamp}.json`;
        }

        const data = {
            timestamp: new Date().toISOString(),
            total_projects: projects.length,
            projects: projects
        };

        try {
            fs.writeFileSync(filename, JSON.stringify(data, null, 2));
            console.log(chalk.green(`✅ 项目数据已保存到: ${filename}`));
            return filename;
        } catch (error) {
            console.error(chalk.red(`保存失败: ${error.message}`));
            return null;
        }
    }

    /**
     * 保存NFT数据到文件
     */
    async saveNFTsData(projectMint, filename = null) {
        const nfts = await this.getProjectNFTs(projectMint);
        const stats = await this.getProjectStats(projectMint);
        
        if (!filename) {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            filename = `nfts-${projectMint.substring(0, 8)}-${timestamp}.json`;
        }

        const data = {
            timestamp: new Date().toISOString(),
            project_mint: projectMint,
            stats: stats,
            total_nfts: nfts.length,
            nfts: nfts
        };

        try {
            fs.writeFileSync(filename, JSON.stringify(data, null, 2));
            console.log(chalk.green(`✅ NFT数据已保存到: ${filename}`));
            return filename;
        } catch (error) {
            console.error(chalk.red(`保存失败: ${error.message}`));
            return null;
        }
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.projectsCache = null;
        this.nftsCache.clear();
        console.log(chalk.blue('🗑️  缓存已清除'));
    }

    /**
     * 检查缓存是否有效
     */
    isValidCache(timestamp) {
        return Date.now() - timestamp < this.cacheExpiry;
    }

    /**
     * 格式化数字
     */
    formatNumber(num) {
        if (!num) return '0';
        return new Intl.NumberFormat().format(num);
    }

    /**
     * 显示项目摘要
     */
    displayProjectSummary(project) {
        console.log(chalk.cyan(`📊 ${project.project_name} (${project.token_symbol})`));
        console.log(chalk.gray(`   地址: ${project.project_mint}`));
        console.log(chalk.gray(`   活跃NFT: ${project.nft_issue_count - project.nft_burn_count}`));
        console.log(chalk.gray(`   24h交易量: $${this.formatNumber(project.volume_24h)}`));
    }

    /**
     * 显示NFT摘要
     */
    displayNFTSummary(nft) {
        const priceSol = nft.price / **********;
        console.log(chalk.cyan(`🎨 NFT ${nft.nft_id}`));
        console.log(chalk.gray(`   价格: ${priceSol.toFixed(6)} SOL`));
        console.log(chalk.gray(`   轮次: ${nft.round}`));
        console.log(chalk.gray(`   状态: ${nft.is_burned === 0 ? '活跃' : '已销毁'}`));
    }
}

export { PPPDataProvider };
