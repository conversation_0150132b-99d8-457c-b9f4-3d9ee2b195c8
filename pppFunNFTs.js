#!/usr/bin/env node

import fetch from 'node-fetch';
import chalk from 'chalk';
import inquirer from 'inquirer';
import fs from 'fs';

class PppFunNFTsAPI {
  constructor() {
    this.baseUrl = 'https://api.ppp.fun';
  }

  // 根据项目 mint 地址获取 NFT 列表
  async getProjectNFTs(projectMint, options = {}) {
    const {
      limit = 100,
      page = 1,
      sort = 'last_trade',
      nft_id = '',
      owner_pubkey = '',
      order = 'asc'
    } = options;

    try {
      const url = `${this.baseUrl}/project/${projectMint}/nfts?limit=${limit}&page=${page}&sort=${sort}&nft_id=${nft_id}&owner_pubkey=${encodeURIComponent(owner_pubkey)}&order=${order}`;
      console.log(chalk.blue(`正在请求: ${url}`));

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'PPP-Fun-NFTs-Tool/1.0.0'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error('API 返回失败状态');
      }

      return data;
    } catch (error) {
      console.error(chalk.red(`请求失败: ${error.message}`));
      throw error;
    }
  }

  // 提取 NFT 关键信息
  extractNFTInfo(nft) {
    return {
      // NFT 基本信息
      nft_pubkey: nft.nft_pubkey,
      mint_pubkey: nft.mint_pubkey,
      owner_pubkey: nft.owner_pubkey,
      nft_id: nft.nft_id,
      
      // 交易信息
      round: nft.round,
      price: nft.price,
      split_count: nft.split_count,
      last_split: nft.last_split,
      last_trade: nft.last_trade,
      
      // 状态信息
      is_burned: nft.is_burned,
      
      // 时间信息
      create_time: nft.create_time,
      updated_at: nft.updated_at,
      unix_ts: nft.unix_ts,
      
      // 计算字段
      price_sol: nft.price / 1000000000, // 转换为 SOL
      is_active: nft.is_burned === 0,
      last_trade_date: new Date(nft.last_trade * 1000).toLocaleString()
    };
  }

  // 显示 NFT 信息
  displayNFT(nft, index) {
    const info = this.extractNFTInfo(nft);
    
    console.log(chalk.blue.bold(`\n🎨 NFT ${index + 1} (ID: ${info.nft_id})`));
    console.log('─'.repeat(50));
    
    console.log(chalk.green.bold('🔑 基本信息:'));
    console.log(`NFT 地址: ${info.nft_pubkey}`);
    console.log(`代币地址: ${info.mint_pubkey}`);
    console.log(`持有者: ${info.owner_pubkey}`);
    console.log(`NFT ID: ${info.nft_id}`);
    
    console.log(chalk.green.bold('\n💰 交易信息:'));
    console.log(`当前价格: ${this.formatNumber(info.price)} lamports (${info.price_sol.toFixed(4)} SOL)`);
    console.log(`轮次: ${info.round}`);
    console.log(`分割次数: ${info.split_count}`);
    console.log(`最后分割: ${info.last_split}`);
    console.log(`最后交易: ${info.last_trade_date}`);
    
    console.log(chalk.green.bold('\n📊 状态信息:'));
    console.log(`状态: ${info.is_active ? chalk.green('活跃') : chalk.red('已销毁')}`);
    console.log(`是否销毁: ${info.is_burned ? '是' : '否'}`);
    
    console.log(chalk.green.bold('\n⏰ 时间信息:'));
    console.log(`创建时间: ${new Date(info.create_time).toLocaleString()}`);
    console.log(`更新时间: ${new Date(info.updated_at).toLocaleString()}`);
  }

  // 显示 NFT 统计信息
  displayNFTStats(nfts) {
    const stats = this.calculateNFTStats(nfts);
    
    console.log(chalk.blue.bold('\n📊 NFT 统计信息:'));
    console.log('═'.repeat(50));
    
    console.log(chalk.green(`总 NFT 数量: ${stats.total}`));
    console.log(chalk.green(`活跃 NFT: ${stats.active}`));
    console.log(chalk.red(`已销毁 NFT: ${stats.burned}`));
    console.log(chalk.yellow(`总价值: ${stats.totalValue.toFixed(4)} SOL`));
    console.log(chalk.yellow(`平均价格: ${stats.averagePrice.toFixed(4)} SOL`));
    console.log(chalk.yellow(`最高价格: ${stats.maxPrice.toFixed(4)} SOL`));
    console.log(chalk.yellow(`最低价格: ${stats.minPrice.toFixed(4)} SOL`));
    console.log(chalk.blue(`总分割次数: ${stats.totalSplits}`));
  }

  // 计算 NFT 统计信息
  calculateNFTStats(nfts) {
    const activeNFTs = nfts.filter(nft => nft.is_burned === 0);
    const burnedNFTs = nfts.filter(nft => nft.is_burned === 1);
    
    const prices = activeNFTs.map(nft => nft.price / 1000000000);
    const totalSplits = nfts.reduce((sum, nft) => sum + nft.split_count, 0);
    
    return {
      total: nfts.length,
      active: activeNFTs.length,
      burned: burnedNFTs.length,
      totalValue: prices.reduce((sum, price) => sum + price, 0),
      averagePrice: prices.length > 0 ? prices.reduce((sum, price) => sum + price, 0) / prices.length : 0,
      maxPrice: prices.length > 0 ? Math.max(...prices) : 0,
      minPrice: prices.length > 0 ? Math.min(...prices) : 0,
      totalSplits
    };
  }

  // 格式化数字
  formatNumber(num) {
    if (!num) return '0';
    return new Intl.NumberFormat().format(num);
  }

  // 保存 NFT 数据
  saveNFTsData(nfts, projectMint, filename) {
    try {
      const extractedData = nfts.map(nft => this.extractNFTInfo(nft));
      const saveData = {
        project_mint: projectMint,
        timestamp: new Date().toISOString(),
        total_nfts: nfts.length,
        stats: this.calculateNFTStats(nfts),
        nfts: extractedData
      };
      
      fs.writeFileSync(filename, JSON.stringify(saveData, null, 2));
      console.log(chalk.green(`✅ NFT 数据已保存到: ${filename}`));
      return filename;
    } catch (error) {
      console.error(chalk.red(`保存失败: ${error.message}`));
      return null;
    }
  }

  // 保存为 CSV 格式
  saveNFTsCSV(nfts, projectMint, filename) {
    try {
      const extractedData = nfts.map(nft => this.extractNFTInfo(nft));
      
      if (extractedData.length === 0) {
        throw new Error('没有数据可保存');
      }

      // CSV 头部
      const headers = Object.keys(extractedData[0]);
      const csvContent = [
        headers.join(','),
        ...extractedData.map(row => 
          headers.map(header => `"${row[header] || ''}"`).join(',')
        )
      ].join('\n');

      fs.writeFileSync(filename, csvContent);
      console.log(chalk.green(`✅ CSV 数据已保存到: ${filename}`));
      return filename;
    } catch (error) {
      console.error(chalk.red(`保存 CSV 失败: ${error.message}`));
      return null;
    }
  }

  // 按持有者分组 NFT
  groupNFTsByOwner(nfts) {
    const groups = {};
    nfts.forEach(nft => {
      const owner = nft.owner_pubkey;
      if (!groups[owner]) {
        groups[owner] = [];
      }
      groups[owner].push(nft);
    });
    return groups;
  }

  // 显示持有者统计
  displayOwnerStats(nfts) {
    const groups = this.groupNFTsByOwner(nfts);
    const owners = Object.keys(groups);
    
    console.log(chalk.blue.bold('\n👥 持有者统计:'));
    console.log('═'.repeat(50));
    
    console.log(chalk.green(`总持有者数量: ${owners.length}`));
    
    // 按持有数量排序
    const sortedOwners = owners.sort((a, b) => groups[b].length - groups[a].length);
    
    console.log(chalk.blue.bold('\n🏆 Top 10 持有者:'));
    sortedOwners.slice(0, 10).forEach((owner, index) => {
      const count = groups[owner].length;
      const totalValue = groups[owner].reduce((sum, nft) => sum + (nft.price / 1000000000), 0);
      console.log(`${index + 1}. ${owner.substring(0, 8)}...${owner.substring(owner.length - 8)} - ${count} NFTs (${totalValue.toFixed(4)} SOL)`);
    });
  }
}

// 主函数
async function main() {
  console.log(chalk.blue.bold('🎨 PPP.Fun NFT 列表获取工具\n'));
  
  const api = new PppFunNFTsAPI();
  
  const { projectMint } = await inquirer.prompt([
    {
      type: 'input',
      name: 'projectMint',
      message: '输入项目 mint 地址:',
      default: 'H23p5B7weYq9ACuQ4Cjg42uPFoJRn4ZXrd1jTa1ZSppp',
      validate: (value) => value.length > 0 || '请输入有效的项目 mint 地址'
    }
  ]);

  const { options } = await inquirer.prompt([
    {
      type: 'list',
      name: 'options',
      message: '选择查询选项:',
      choices: [
        { name: '📋 获取所有 NFT', value: 'all' },
        { name: '🔍 按持有者查询', value: 'owner' },
        { name: '🎯 按 NFT ID 查询', value: 'nft_id' },
        { name: '⚙️ 自定义查询', value: 'custom' }
      ]
    }
  ]);

  let queryOptions = {};

  try {
    switch (options) {
      case 'all':
        queryOptions = { limit: 100 };
        break;

      case 'owner':
        const { ownerPubkey } = await inquirer.prompt([
          {
            type: 'input',
            name: 'ownerPubkey',
            message: '输入持有者地址:',
            validate: (value) => value.length > 0 || '请输入持有者地址'
          }
        ]);
        queryOptions = { owner_pubkey: ownerPubkey, limit: 100 };
        break;

      case 'nft_id':
        const { nftId } = await inquirer.prompt([
          {
            type: 'input',
            name: 'nftId',
            message: '输入 NFT ID:',
            validate: (value) => !isNaN(value) || '请输入有效的数字'
          }
        ]);
        queryOptions = { nft_id: nftId, limit: 100 };
        break;

      case 'custom':
        queryOptions = await inquirer.prompt([
          {
            type: 'number',
            name: 'limit',
            message: '获取数量:',
            default: 100
          },
          {
            type: 'number',
            name: 'page',
            message: '页码:',
            default: 1
          },
          {
            type: 'list',
            name: 'sort',
            message: '排序方式:',
            choices: ['last_trade', 'price', 'nft_id', 'create_time'],
            default: 'last_trade'
          },
          {
            type: 'list',
            name: 'order',
            message: '排序顺序:',
            choices: ['asc', 'desc'],
            default: 'asc'
          }
        ]);
        break;
    }

    const result = await api.getProjectNFTs(projectMint, queryOptions);
    
    if (result && result.data && result.data.length > 0) {
      console.log(chalk.green.bold(`\n✅ 成功获取 ${result.data.length} 个 NFT:`));
      
      // 显示统计信息
      api.displayNFTStats(result.data);
      
      // 显示持有者统计
      api.displayOwnerStats(result.data);
      
      // 显示详细 NFT 信息
      const { showDetails } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'showDetails',
          message: '是否显示详细 NFT 信息?',
          default: false
        }
      ]);

      if (showDetails) {
        result.data.forEach((nft, index) => {
          api.displayNFT(nft, index);
        });
      }

      // 询问是否保存
      const { save } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'save',
          message: '是否保存数据到文件?',
          default: true
        }
      ]);

      if (save) {
        const { format } = await inquirer.prompt([
          {
            type: 'list',
            name: 'format',
            message: '选择保存格式:',
            choices: ['json', 'csv']
          }
        ]);

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `ppp-fun-nfts-${projectMint.substring(0, 8)}-${timestamp}.${format}`;

        if (format === 'json') {
          api.saveNFTsData(result.data, projectMint, filename);
        } else {
          api.saveNFTsCSV(result.data, projectMint, filename);
        }
      }

    } else {
      console.log(chalk.yellow('未找到 NFT 数据'));
    }

  } catch (error) {
    console.error(chalk.red(`操作失败: ${error.message}`));
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.log(chalk.red(`❌ 未捕获的异常: ${error.message}`));
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.log(chalk.red(`❌ 未处理的Promise拒绝: ${reason}`));
  process.exit(1);
});

// 启动
main().catch(console.error);
