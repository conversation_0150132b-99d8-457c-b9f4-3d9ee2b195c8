#!/usr/bin/env node

import { PPPProjectNFTBuyer } from './ppp-project-nft-buyer.js';
import chalk from 'chalk';
import inquirer from 'inquirer';
import fs from 'fs';

/**
 * 交互式PPP项目NFT购买工具
 * 
 * 功能流程:
 * 1. 选择或搜索项目
 * 2. 查看项目的NFT列表
 * 3. 选择要购买的NFT
 * 4. 执行购买操作
 */

async function main() {
    console.log(chalk.blue.bold('🎯 PPP项目NFT购买工具\n'));
    console.log(chalk.gray('这个工具可以帮您浏览项目、查看NFT并执行购买操作\n'));

    const buyer = new PPPProjectNFTBuyer();

    try {
        // 1. 初始化设置
        console.log(chalk.blue.bold('📋 步骤 1: 初始化设置'));
        
        const { rpcUrl, walletPath } = await inquirer.prompt([
            {
                type: 'list',
                name: 'rpcUrl',
                message: '选择Solana网络:',
                choices: [
                    { name: '🌐 主网 (Mainnet)', value: 'https://api.mainnet-beta.solana.com' },
                    { name: '🧪 测试网 (Devnet)', value: 'https://api.devnet.solana.com' },
                    { name: '🔧 自定义RPC', value: 'custom' }
                ],
                default: 'https://api.mainnet-beta.solana.com'
            },
            {
                type: 'input',
                name: 'walletPath',
                message: '钱包文件路径 (可选，不提供则只能查看):',
                default: '',
                when: () => true
            }
        ]);

        let finalRpcUrl = rpcUrl;
        if (rpcUrl === 'custom') {
            const { customRpc } = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'customRpc',
                    message: '输入自定义RPC URL:',
                    validate: (value) => value.startsWith('http') || '请输入有效的HTTP URL'
                }
            ]);
            finalRpcUrl = customRpc;
        }

        await buyer.initialize(finalRpcUrl, walletPath || null);

        // 2. 选择项目
        console.log(chalk.blue.bold('\n📋 步骤 2: 选择项目'));
        
        const { projectAction } = await inquirer.prompt([
            {
                type: 'list',
                name: 'projectAction',
                message: '如何选择项目:',
                choices: [
                    { name: '🔍 搜索项目', value: 'search' },
                    { name: '📋 浏览项目列表', value: 'browse' },
                    { name: '🎯 直接输入项目地址', value: 'direct' }
                ]
            }
        ]);

        let selectedProject = null;
        let projectMint = null;

        switch (projectAction) {
            case 'search':
                const { keyword } = await inquirer.prompt([
                    {
                        type: 'input',
                        name: 'keyword',
                        message: '输入搜索关键词:',
                        validate: (value) => value.length > 0 || '请输入搜索关键词'
                    }
                ]);

                const searchResult = await buyer.getProjects({ keyword, limit: 10 });
                if (searchResult.data && searchResult.data.length > 0) {
                    const { projectChoice } = await inquirer.prompt([
                        {
                            type: 'list',
                            name: 'projectChoice',
                            message: '选择项目:',
                            choices: searchResult.data.map((project, index) => ({
                                name: `${project.project_name} (${project.token_symbol}) - ${project.nft_issue_count - project.nft_burn_count} 活跃NFT`,
                                value: index
                            }))
                        }
                    ]);
                    selectedProject = searchResult.data[projectChoice];
                    projectMint = selectedProject.project_mint;
                } else {
                    console.log(chalk.yellow('未找到匹配的项目'));
                    return;
                }
                break;

            case 'browse':
                const browseResult = await buyer.getProjects({ limit: 20 });
                if (browseResult.data && browseResult.data.length > 0) {
                    const { projectChoice } = await inquirer.prompt([
                        {
                            type: 'list',
                            name: 'projectChoice',
                            message: '选择项目:',
                            choices: browseResult.data.map((project, index) => ({
                                name: `${project.project_name} (${project.token_symbol}) - $${buyer.formatNumber(project.volume_24h)} 24h交易量`,
                                value: index
                            }))
                        }
                    ]);
                    selectedProject = browseResult.data[projectChoice];
                    projectMint = selectedProject.project_mint;
                } else {
                    console.log(chalk.yellow('未找到项目'));
                    return;
                }
                break;

            case 'direct':
                const { directMint } = await inquirer.prompt([
                    {
                        type: 'input',
                        name: 'directMint',
                        message: '输入项目mint地址:',
                        validate: (value) => value.length > 0 || '请输入项目mint地址'
                    }
                ]);
                projectMint = directMint;
                break;
        }

        if (!projectMint) {
            console.log(chalk.red('未选择项目'));
            return;
        }

        // 显示选中的项目信息
        if (selectedProject) {
            buyer.displayProject(selectedProject, 0);
        }

        // 3. 获取并显示NFT列表
        console.log(chalk.blue.bold('\n📋 步骤 3: 获取NFT列表'));
        
        const { nftOptions } = await inquirer.prompt([
            {
                type: 'list',
                name: 'nftOptions',
                message: '选择NFT查询方式:',
                choices: [
                    { name: '📋 获取所有活跃NFT', value: 'all' },
                    { name: '💰 按价格排序', value: 'price' },
                    { name: '⏰ 按最近交易排序', value: 'recent' },
                    { name: '🎯 按NFT ID查询', value: 'id' }
                ]
            }
        ]);

        let nftQueryOptions = {};
        switch (nftOptions) {
            case 'all':
                nftQueryOptions = { limit: 50, sort: 'nft_id', order: 'asc' };
                break;
            case 'price':
                nftQueryOptions = { limit: 50, sort: 'price', order: 'asc' };
                break;
            case 'recent':
                nftQueryOptions = { limit: 50, sort: 'last_trade', order: 'desc' };
                break;
            case 'id':
                const { nftId } = await inquirer.prompt([
                    {
                        type: 'input',
                        name: 'nftId',
                        message: '输入NFT ID:',
                        validate: (value) => !isNaN(value) || '请输入有效的数字'
                    }
                ]);
                nftQueryOptions = { nft_id: nftId, limit: 10 };
                break;
        }

        const nftResult = await buyer.getProjectNFTs(projectMint, nftQueryOptions);
        
        if (!nftResult.data || nftResult.data.length === 0) {
            console.log(chalk.yellow('未找到NFT'));
            return;
        }

        // 过滤活跃NFT
        const activeNFTs = nftResult.data.filter(nft => nft.is_burned === 0);
        
        if (activeNFTs.length === 0) {
            console.log(chalk.yellow('没有活跃的NFT可供购买'));
            return;
        }

        console.log(chalk.green.bold(`\n✅ 找到 ${activeNFTs.length} 个活跃NFT:`));
        
        // 显示NFT统计
        const prices = activeNFTs.map(nft => nft.price / 1000000000);
        const totalValue = prices.reduce((sum, price) => sum + price, 0);
        const avgPrice = totalValue / prices.length;
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);

        console.log(chalk.blue(`总价值: ${totalValue.toFixed(4)} SOL`));
        console.log(chalk.blue(`平均价格: ${avgPrice.toFixed(6)} SOL`));
        console.log(chalk.blue(`价格范围: ${minPrice.toFixed(6)} - ${maxPrice.toFixed(6)} SOL`));

        // 4. 选择要购买的NFT
        console.log(chalk.blue.bold('\n📋 步骤 4: 选择NFT'));
        
        const { showNFTs } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'showNFTs',
                message: '是否显示NFT详细信息?',
                default: true
            }
        ]);

        if (showNFTs) {
            activeNFTs.slice(0, 10).forEach((nft, index) => {
                buyer.displayNFT(nft, index);
            });
            
            if (activeNFTs.length > 10) {
                console.log(chalk.gray(`\n... 还有 ${activeNFTs.length - 10} 个NFT未显示`));
            }
        }

        const { nftChoice } = await inquirer.prompt([
            {
                type: 'list',
                name: 'nftChoice',
                message: '选择要购买的NFT:',
                choices: [
                    ...activeNFTs.slice(0, 20).map((nft, index) => ({
                        name: `NFT ${nft.nft_id} - ${(nft.price / 1000000000).toFixed(6)} SOL (轮次: ${nft.round})`,
                        value: index
                    })),
                    { name: '❌ 不购买', value: -1 }
                ]
            }
        ]);

        if (nftChoice === -1) {
            console.log(chalk.yellow('未选择NFT，程序结束'));
            return;
        }

        const selectedNFT = activeNFTs[nftChoice];
    console.log(chalk.green.bold('\n🎉======================================================================!'));
        console.log(JSON.stringify(selectedNFT));

        // 5. 执行购买
        if (buyer.wallet) {
            console.log(chalk.blue.bold('\n📋 步骤 5: 执行购买'));
            
            try {
                const result = await buyer.buyNFT(selectedNFT, projectMint);
                
                if (result) {
                    // 保存购买记录
                    const purchaseRecord = {
                        timestamp: new Date().toISOString(),
                        project_mint: projectMint,
                        project_name: selectedProject?.project_name || 'Unknown',
                        nft: selectedNFT,
                        transaction: result,
                        buyer: buyer.wallet.publicKey.toString()
                    };
                    
                    const filename = `purchase-record-${Date.now()}.json`;
                    buyer.saveData(purchaseRecord, filename);
                    
                    console.log(chalk.green.bold('\n🎉 购买完成!'));
                    console.log(chalk.blue(`查看交易: https://solscan.io/tx/${result.signature}`));
                }
            } catch (error) {
                console.error(chalk.red(`购买失败: ${error.message}`));
            }
        } else {
            console.log(chalk.yellow('\n⚠️  未加载钱包，无法执行购买操作'));
            console.log(chalk.gray('如需购买，请重新运行程序并提供钱包文件路径'));
        }

    } catch (error) {
        console.error(chalk.red(`程序执行失败: ${error.message}`));
    }
}

// 错误处理
process.on('uncaughtException', (error) => {
    console.log(chalk.red(`❌ 未捕获的异常: ${error.message}`));
    process.exit(1);
});

process.on('unhandledRejection', (reason) => {
    console.log(chalk.red(`❌ 未处理的Promise拒绝: ${reason}`));
    process.exit(1);
});

// 启动程序
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export { main };
