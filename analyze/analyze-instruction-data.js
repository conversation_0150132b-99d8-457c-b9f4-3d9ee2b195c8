#!/usr/bin/env node

/**
 * 分析PPP程序的真实指令数据
 */

// 从交易记录中提取的Base64指令数据
const base64Data = "81t3Q/0zW+kKmaJInCt3UzBVp4zOJ9Vosd4jgyPHt8QfTyV8uxHUUf/u/bc/934W7srK6TCh3KnyR0cbRpnMhJN5OR9gX+dyRgAAAAAAAADzAQAAAAAAAAQAAAAAAAAAdxDSDD/HwTaHnnfHxE+uFLTYN5OCTiny2T9qfg5S145Q1AqUNzQopH3gZX6inB8QBr1bxzaX+3Sw0FRA5rsAPFAyoisAAAAAAOQLVAIAAAA5v3RoAAAAAAMAAABCdXk=";

console.log('🔍 分析PPP程序指令数据\n');

try {
    // 解码Base64数据
    const buffer = Buffer.from(base64Data, 'base64');
    console.log(`📊 指令数据长度: ${buffer.length} 字节`);
    console.log(`📊 十六进制数据: ${buffer.toString('hex')}`);
    
    // 分析前几个字节（通常是discriminator）
    console.log('\n🔍 前16字节分析:');
    for (let i = 0; i < Math.min(16, buffer.length); i++) {
        console.log(`  字节 ${i}: ${buffer[i]} (0x${buffer[i].toString(16).padStart(2, '0')})`);
    }
    
    // 查找可能的discriminator
    console.log('\n🎯 可能的discriminator:');
    console.log(`  第1个字节: ${buffer[0]} (0x${buffer[0].toString(16)})`);
    console.log(`  前4字节: ${buffer.readUInt32LE(0)} (小端序)`);
    console.log(`  前8字节: ${buffer.readBigUInt64LE(0)} (小端序)`);
    
    // 查找金额数据 (732050000 lamports = 0.732050000 SOL)
    const targetAmount = 732050000;
    console.log(`\n💰 查找金额数据 (${targetAmount} lamports):`);
    
    for (let i = 0; i <= buffer.length - 8; i++) {
        try {
            const value = buffer.readBigUInt64LE(i);
            if (value == targetAmount) {
                console.log(`  ✅ 在位置 ${i} 找到金额: ${value}`);
            }
        } catch (e) {
            // 忽略读取错误
        }
    }
    
    // 尝试不同的discriminator值
    console.log('\n🧪 测试不同的discriminator:');
    const possibleDiscriminators = [
        buffer[0],
        buffer.readUInt32LE(0) & 0xFF,
        buffer.readUInt32LE(0),
        buffer.readBigUInt64LE(0) & 0xFFn
    ];
    
    possibleDiscriminators.forEach((disc, index) => {
        console.log(`  选项 ${index + 1}: ${disc} (0x${disc.toString(16)})`);
    });
    
    // 分析指令结构
    console.log('\n📋 指令结构分析:');
    console.log('基于Anchor程序的典型结构:');
    console.log('- 前8字节: 指令discriminator');
    console.log('- 后续字节: 指令参数');
    
    if (buffer.length >= 8) {
        const discriminator = buffer.readBigUInt64LE(0);
        console.log(`\n🎯 推荐使用的discriminator: ${discriminator} (0x${discriminator.toString(16)})`);
        
        // 显示参数部分
        if (buffer.length > 8) {
            const params = buffer.slice(8);
            console.log(`📊 参数部分长度: ${params.length} 字节`);
            console.log(`📊 参数十六进制: ${params.toString('hex')}`);
        }
    }
    
} catch (error) {
    console.error('❌ 分析失败:', error.message);
}

console.log('\n✅ 分析完成');
console.log('💡 建议: 使用分析出的discriminator更新ppp-buy-script.js中的指令构建');
