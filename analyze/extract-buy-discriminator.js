#!/usr/bin/env node

import { Transaction } from '@solana/web3.js';
import chalk from 'chalk';

// 真正的buy交易数据
const buyTransactionData = "AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAgULKc67EPw55sWqYiwTA4T5gqg1a6RuEWZn0TTKXyY/zwoAMSQ/YLSGNl6IAGJlp2QHHN6SQum434HAlKaKdrM3Tcyey7jFG9oo5d8gneoEi9axb/nSzAUqrqv4AnH4ChiOdt8W7gJVHjKh1UF7v6Azvm7VL1xWmzEEA5VUTG3OMABLce+dHFL/a925J3dv9e50H61miO8c5Jc4EFcyo/ex13xG0EHy68lclWoV2B3y2Ln0ZSBflATXEs2wVafd65qtf9v2cKNuw8O7NzMCcw47S3ZWykXjlE+CsASGYuhHyzMOxBYtswOAif5tIIgkKrqB73QV9cD24C1q8DdkbxJScYZSrsc44lZqblp2/NR/n3oz1Sy3PqyQ2k1cnnqKYC61vXVRZbfq9d/EyHYkqVNHDUucFpR861x7VHVKpFiR0ztObibFSKoB9JIv1+T4l9al60SyUgJgJVASPIIXieC7AuNw0A/CO6h58hRCLX4NxLsAYATMnNis8o3ZNClvc3oAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABK2QbdSssl2FYiG4vmZxMcCvWsw918ySPX08i1yJ0ZWakqWotPKVlShCVQqpP9W5W1rOao65IMk5QuQ2kMIOxzsyE/uov5yH+pHkeBlijDg+AL6n6Yx6A+A7oQac/D9vPrANn1spK0IUrH0De01vBkULlkYA3zcwUrtehPL46aZwW8FW2DBFnM6lG7gdIDO9TKvFnVh25d/A166eylFBgiBpuIV/6rgYT7aH9jRhjANdrEOdwa6ztVmKDwAAAAAAEG3fbh12Whk9nL4UbO63msHLSF7V9bN5E6jPWFfv8Aqf/XWppZ+KQqfrIsMl20octTcjru73lViw/POvCbHxj0AREXDgAQDwILCQQDExMSDQcMEwUIARILCgYIZgY9EgHa6+o=";

console.log(chalk.blue.bold('🔍 提取真正的Buy Discriminator\n'));

try {
    // 解码交易
    const buffer = Buffer.from(buyTransactionData, 'base64');
    const transaction = Transaction.from(buffer);
    
    console.log(chalk.green(`✅ 交易解析成功，指令数量: ${transaction.instructions.length}`));
    
    // 找到PPP指令
    const pppInstruction = transaction.instructions.find(
        inst => inst.programId.toString() === 'PPP5a1E95YMFv8Rmhvc7PBW6SSfHfvNKfAz7988xvB3'
    );
    
    if (pppInstruction) {
        console.log(chalk.yellow.bold('🎯 找到PPP Buy指令!'));
        console.log(chalk.blue(`📊 账户数量: ${pppInstruction.keys.length}`));
        console.log(chalk.blue(`📊 数据长度: ${pppInstruction.data.length} 字节`));
        console.log(chalk.blue(`📊 完整数据: ${pppInstruction.data.toString('hex')}`));
        
        // 提取discriminator
        const discriminator = pppInstruction.data.slice(0, 8);
        console.log(chalk.green.bold(`\n✅ 正确的Buy Discriminator: ${discriminator.toString('hex')}`));
        
        // 对比之前错误的discriminator
        const wrongDiscriminator = '3339e12fb69289a6';
        const correctDiscriminator = discriminator.toString('hex');
        
        console.log(chalk.blue.bold('\n📊 Discriminator对比:'));
        console.log(chalk.red(`❌ 错误 (Mint): ${wrongDiscriminator}`));
        console.log(chalk.green(`✅ 正确 (Buy):  ${correctDiscriminator}`));
        
        // 分析剩余数据
        if (pppInstruction.data.length > 8) {
            console.log(chalk.blue.bold('\n💰 分析指令参数:'));
            const params = pppInstruction.data.slice(8);
            console.log(chalk.gray(`参数数据: ${params.toString('hex')}`));
            console.log(chalk.gray(`参数长度: ${params.length} 字节`));
            
            // 如果有参数，尝试解析
            if (params.length >= 8) {
                try {
                    const possibleAmount = params.readBigUInt64LE(0);
                    const solAmount = Number(possibleAmount) / 1000000000;
                    console.log(chalk.yellow(`可能的金额: ${possibleAmount} lamports (${solAmount.toFixed(6)} SOL)`));
                } catch (e) {
                    console.log(chalk.gray('无法解析为金额'));
                }
            }
        }
        
        // 分析签名者
        const signers = pppInstruction.keys.filter(key => key.isSigner);
        console.log(chalk.blue.bold('\n👤 签名者分析:'));
        console.log(chalk.green(`签名者数量: ${signers.length}`));
        signers.forEach((signer, index) => {
            console.log(chalk.gray(`  签名者 ${index + 1}: ${signer.pubkey.toString()}`));
        });
        
        console.log(chalk.blue.bold('\n🔧 立即修复代码:'));
        console.log(chalk.yellow('需要将 ppp-buy-script.js 中的 discriminator 更新为:'));
        console.log(chalk.green(`Buffer.from('${correctDiscriminator}', 'hex')`));
        
    } else {
        console.log(chalk.red('❌ 未找到PPP程序指令'));
    }
    
} catch (error) {
    console.error(chalk.red(`❌ 分析失败: ${error.message}`));
}

console.log(chalk.blue.bold('\n🎉 分析完成!'));
