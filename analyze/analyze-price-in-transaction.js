#!/usr/bin/env node

import { Transaction } from '@solana/web3.js';
import chalk from 'chalk';

/**
 * 分析真实交易数据中是否包含价格信息
 */
async function analyzePriceInTransaction() {
    console.log(chalk.blue.bold('🔍 分析交易数据中的价格信息\n'));

    // 用户提供的真实交易数据
    const transactionData = "AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAoYLKc67EPw55sWqYiwTA4T5gqg1a6RuEWZn0TTKXyY/zwVMJ93fKDfvXJ+S1DPaqjFkGH9kI8kX0Sp+JgAHHcz2UUmcRFp0nBBGJVh6+/FeNN9bVyEzm9gIOS8f6Vfg0XSYAhVPuDfVVWBXximUSYXqhNemkB/q8XuSpm6n/9GAd1rk9uhRkH+IvTqMDcV3TJ0ElyPleYp2p9FuqOjpjhZO3j4kewwSqd7NhvIrd5MFJhSbklJuolp1uUH4bXRKNNXtf9v2cKNuw8O7NzMCcw47S3ZWykXjlE+CsASGYuhHyy3fA6qtk5XrGeWresHmkh5hzC7TuZuh8u/BPS0Lr3s8bfQIlJUrAfjsr0/hsHw8RA/wHCMwVrvFAc6pkU/Vepp0E4fdhhGEc8Yrj+nEDZLLF215BpJq91Yfvu1gkOvJrnp1UmEjwHa9rZ1VKTcE+uUUUkcgoxNPNY9MleWOy4+2PeNVozGo0Pzota5SliFHAE8PcXZZ3wT0WDk8SBgCHom8q27pT/4Jyjxq+Ub0RJTwu0Fx8L4qPdMxx++UQmdPIAKmQ2GVn5m94O0bquBY9KJknDpmGMQuDD0fZUkPDXoZgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYNzKytmF6srPaYrkS8Cg8XVY7nAtIOQjYGl5bI60gzmMlyWPTiSJ8bs9ECkUjg2DC1oTmdr/EIQEjnvY2+n4WakqWotPKVlShCVQqpP9W5W1rOao65IMk5QuQ2kMIOxzsyE/uov5yH+pHkeBlijDg+AL6n6Yx6A+A7oQac/D9vPrANn1spK0IUrH0De01vBkULlkYA3zcwUrtehPL46aZwW8FW2DBFnM6lG7gdIDO9TKvFnVh25d/A166eylFBgiBpuIV/6rgYT7aH9jRhjANdrEOdwa6ztVmKDwAAAAAAEGp9UXGSxcUSGMyUw9SvF/WNruCJuh/UTj29mKAAAAAAbd9uHXZaGT2cvhRs7reawctIXtX1s3kTqM9YV+/wCpy5KiY7Yljdkc5+d6QhV7QZkl+9mqd5arzXzEQhvuWKMBFBsRAAESEwwVDwkLBw0ECggCFxcXEA4WBQMVCwYIMznhL7aSiaY=";

    try {
        // 解码Base64交易数据
        const buffer = Buffer.from(transactionData, 'base64');
        console.log(chalk.green(`✅ 交易数据长度: ${buffer.length} 字节`));

        // 反序列化交易
        const transaction = Transaction.from(buffer);
        console.log(chalk.green(`✅ 交易解析成功`));

        // 找到PPP指令
        const pppInstruction = transaction.instructions.find(
            inst => inst.programId.toString() === 'PPP5a1E95YMFv8Rmhvc7PBW6SSfHfvNKfAz7988xvB3'
        );

        if (!pppInstruction) {
            console.log(chalk.red('❌ 未找到PPP程序指令'));
            return;
        }

        console.log(chalk.blue.bold('\n🎯 PPP指令分析:'));
        console.log(chalk.green(`指令数据: ${pppInstruction.data.toString('hex')}`));
        console.log(chalk.green(`数据长度: ${pppInstruction.data.length} 字节`));

        // 分析指令数据
        if (pppInstruction.data.length === 8) {
            console.log(chalk.yellow('\n📊 指令数据分析:'));
            console.log(chalk.blue('这是一个8字节的discriminator，不包含额外参数'));
            console.log(chalk.blue('这意味着价格信息不在指令数据中'));
        }

        // 检查是否有其他指令包含价格信息
        console.log(chalk.blue.bold('\n🔍 检查所有指令:'));
        
        transaction.instructions.forEach((instruction, index) => {
            console.log(chalk.blue(`\n指令 ${index + 1}:`));
            console.log(chalk.gray(`  程序ID: ${instruction.programId.toString()}`));
            console.log(chalk.gray(`  数据长度: ${instruction.data.length} 字节`));
            
            if (instruction.data.length > 8) {
                console.log(chalk.gray(`  数据: ${instruction.data.toString('hex')}`));
                
                // 在较长的指令数据中查找可能的价格
                console.log(chalk.yellow('  🔍 查找可能的价格数据:'));
                
                for (let i = 0; i <= instruction.data.length - 8; i++) {
                    try {
                        const value = instruction.data.readBigUInt64LE(i);
                        
                        // 检查是否在合理的价格范围内 (0.001 SOL 到 100 SOL)
                        if (value >= 1000000 && value <= 100000000000) {
                            const solValue = Number(value) / 1000000000;
                            console.log(chalk.green(`    位置 ${i}: ${value} lamports (${solValue.toFixed(6)} SOL)`));
                        }
                    } catch (e) {
                        // 忽略读取错误
                    }
                }
            } else {
                console.log(chalk.gray(`  数据: ${instruction.data.toString('hex')} (可能是discriminator)`));
            }
        });

        // 分析账户余额变化 (这里只能分析结构，无法获取实际余额)
        console.log(chalk.blue.bold('\n💰 价格信息可能的位置:'));
        console.log(chalk.yellow('1. 不在指令数据中 - PPP指令只有8字节discriminator'));
        console.log(chalk.yellow('2. 可能在程序内部计算 - 基于NFT当前状态'));
        console.log(chalk.yellow('3. 可能在账户数据中 - 需要读取NFT账户状态'));
        console.log(chalk.yellow('4. 可能在其他指令中 - 如果有预处理指令'));

        // 检查是否有转账指令
        console.log(chalk.blue.bold('\n💸 检查转账相关指令:'));
        
        let foundTransfer = false;
        transaction.instructions.forEach((instruction, index) => {
            // 检查是否是系统程序的转账指令
            if (instruction.programId.toString() === '11111111111111111111111111111111') {
                console.log(chalk.green(`指令 ${index + 1}: 系统程序指令 (可能包含SOL转账)`));
                
                if (instruction.data.length >= 12) {
                    // 系统程序转账指令格式: [指令类型(4字节), 金额(8字节)]
                    try {
                        const instructionType = instruction.data.readUInt32LE(0);
                        if (instructionType === 2) { // Transfer指令
                            const amount = instruction.data.readBigUInt64LE(4);
                            const solAmount = Number(amount) / 1000000000;
                            console.log(chalk.green(`  ✅ 找到转账: ${amount} lamports (${solAmount.toFixed(6)} SOL)`));
                            foundTransfer = true;
                        }
                    } catch (e) {
                        console.log(chalk.gray(`  数据解析失败: ${e.message}`));
                    }
                }
            }
            
            // 检查是否是Token程序的转账指令
            if (instruction.programId.toString() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                console.log(chalk.green(`指令 ${index + 1}: Token程序指令 (可能包含代币转账)`));
                
                if (instruction.data.length >= 9) {
                    try {
                        const instructionType = instruction.data.readUInt8(0);
                        if (instructionType === 3) { // Transfer指令
                            const amount = instruction.data.readBigUInt64LE(1);
                            console.log(chalk.green(`  ✅ 找到代币转账: ${amount} 单位`));
                            foundTransfer = true;
                        }
                    } catch (e) {
                        console.log(chalk.gray(`  数据解析失败: ${e.message}`));
                    }
                }
            }
        });

        if (!foundTransfer) {
            console.log(chalk.yellow('未找到明显的转账指令'));
        }

        // 总结
        console.log(chalk.blue.bold('\n📊 分析结论:'));
        console.log(chalk.red('❌ PPP指令数据中不包含价格信息'));
        console.log(chalk.yellow('⚠️  价格可能由PPP程序内部根据NFT状态计算'));
        console.log(chalk.blue('💡 建议: 价格应该从NFT的当前状态中获取，而不是在指令中指定'));
        
        console.log(chalk.blue.bold('\n🔧 实现建议:'));
        console.log(chalk.green('1. PPP buy指令只需要discriminator (8字节)'));
        console.log(chalk.green('2. 价格由程序根据NFT当前状态自动计算'));
        console.log(chalk.green('3. 买家只需要确保有足够的余额'));
        console.log(chalk.green('4. 不需要在指令中指定购买金额'));

    } catch (error) {
        console.error(chalk.red(`❌ 分析失败: ${error.message}`));
        console.error(chalk.gray(error.stack));
    }
}

// 运行分析
analyzePriceInTransaction().catch(console.error);
