#!/usr/bin/env node

import chalk from 'chalk';

/**
 * 分析discriminator对应的指令类型
 * 当前错误显示指令被识别为 "Mint"，但我们需要 "Buy"
 */
async function analyzeDiscriminatorType() {
    console.log(chalk.blue.bold('🔍 分析Discriminator指令类型\n'));

    console.log(chalk.yellow('当前问题:'));
    console.log(chalk.red('- 使用discriminator: 3339e12fb69289a6'));
    console.log(chalk.red('- 程序识别为: "Mint" 指令'));
    console.log(chalk.red('- 但我们需要: "Buy" 指令'));

    console.log(chalk.blue.bold('\n🔍 Anchor Discriminator 工作原理:'));
    console.log(chalk.blue('Anchor使用函数名的SHA256哈希的前8字节作为discriminator'));
    
    // 计算一些可能的指令名称的discriminator
    const crypto = await import('crypto');
    
    const possibleInstructions = [
        'global:mint',
        'global:buy', 
        'global:purchase',
        'global:trade',
        'global:swap',
        'mint',
        'buy',
        'purchase', 
        'trade',
        'swap'
    ];

    console.log(chalk.blue.bold('\n📊 可能的指令discriminator:'));
    
    for (const instruction of possibleInstructions) {
        const hash = crypto.createHash('sha256').update(instruction).digest();
        const discriminator = hash.slice(0, 8).toString('hex');
        
        console.log(chalk.gray(`${instruction.padEnd(20)} -> ${discriminator}`));
        
        if (discriminator === '3339e12fb69289a6') {
            console.log(chalk.green(`  ✅ 匹配! 这是 "${instruction}" 指令`));
        }
    }

    console.log(chalk.blue.bold('\n🔍 从错误日志分析:'));
    console.log(chalk.yellow('程序日志显示: "Instruction: Mint"'));
    console.log(chalk.blue('这意味着 3339e12fb69289a6 确实是 mint 指令的discriminator'));

    console.log(chalk.blue.bold('\n💡 可能的解决方案:'));
    console.log(chalk.green('1. 查找正确的 "buy" 指令discriminator'));
    console.log(chalk.green('2. 分析PPP程序的IDL文件'));
    console.log(chalk.green('3. 查看其他成功的buy交易'));
    console.log(chalk.green('4. 检查是否需要先调用其他指令'));

    console.log(chalk.blue.bold('\n🔧 下一步行动:'));
    console.log(chalk.yellow('1. 需要找到真正的PPP buy交易记录'));
    console.log(chalk.yellow('2. 当前的交易可能是mint操作，不是buy操作'));
    console.log(chalk.yellow('3. 或者PPP的购买流程可能包含多个步骤'));

    // 检查是否是已知的Anchor指令
    const knownDiscriminators = {
        '3339e12fb69289a6': 'mint (已确认)',
        '66063d1201000000': 'buy (常见)',
        'f223c68952410000': 'purchase (常见)',
        'b712469c946da122': 'swap (常见)'
    };

    console.log(chalk.blue.bold('\n📋 已知的常见discriminator:'));
    for (const [disc, name] of Object.entries(knownDiscriminators)) {
        console.log(chalk.gray(`${disc} -> ${name}`));
    }

    console.log(chalk.blue.bold('\n🎯 结论:'));
    console.log(chalk.red('❌ 当前使用的是 mint 指令，不是 buy 指令'));
    console.log(chalk.yellow('⚠️  需要找到正确的 buy 指令discriminator'));
    console.log(chalk.blue('💡 建议查看PPP程序的完整IDL或其他buy交易'));
}

// 运行分析
analyzeDiscriminatorType().catch(console.error);
