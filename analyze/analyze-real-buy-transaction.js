#!/usr/bin/env node

import { Transaction } from '@solana/web3.js';
import chalk from 'chalk';

/**
 * 分析真正的PPP Buy交易数据
 */
async function analyzeRealBuyTransaction() {
    console.log(chalk.blue.bold('🔍 分析真正的PPP Buy交易\n'));

    // 用户提供的真正的buy交易数据
    const buyTransactionData = "AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAgULKc67EPw55sWqYiwTA4T5gqg1a6RuEWZn0TTKXyY/zwoAMSQ/YLSGNl6IAGJlp2QHHN6SQum434HAlKaKdrM3Tcyey7jFG9oo5d8gneoEi9axb/nSzAUqrqv4AnH4ChiOdt8W7gJVHjKh1UF7v6Azvm7VL1xWmzEEA5VUTG3OMABLce+dHFL/a925J3dv9e50H61miO8c5Jc4EFcyo/ex13xG0EHy68lclWoV2B3y2Ln0ZSBflATXEs2wVafd65qtf9v2cKNuw8O7NzMCcw47S3ZWykXjlE+CsASGYuhHyzMOxBYtswOAif5tIIgkKrqB73QV9cD24C1q8DdkbxJScYZSrsc44lZqblp2/NR/n3oz1Sy3PqyQ2k1cnnqKYC61vXVRZbfq9d/EyHYkqVNHDUucFpR861x7VHVKpFiR0ztObibFSKoB9JIv1+T4l9al60SyUgJgJVASPIIXieC7AuNw0A/CO6h58hRCLX4NxLsAYATMnNis8o3ZNClvc3oAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABK2QbdSssl2FYiG4vmZxMcCvWsw918ySPX08i1yJ0ZWakqWotPKVlShCVQqpP9W5W1rOao65IMk5QuQ2kMIOxzsyE/uov5yH+pHkeBlijDg+AL6n6Yx6A+A7oQac/D9vPrANn1spK0IUrH0De01vBkULlkYA3zcwUrtehPL46aZwW8FW2DBFnM6lG7gdIDO9TKvFnVh25d/A166eylFBgiBpuIV/6rgYT7aH9jRhjANdrEOdwa6ztVmKDwAAAAAAEG3fbh12Whk9nL4UbO63msHLSF7V9bN5E6jPWFfv8Aqf/XWppZ+KQqfrIsMl20octTcjru73lViw/POvCbHxj0AREXDgAQDwILCQQDExMSDQcMEwUIARILCgYIZgY9EgHa6+o=";

    try {
        // 解码Base64交易数据
        const buffer = Buffer.from(buyTransactionData, 'base64');
        console.log(chalk.green(`✅ Buy交易数据长度: ${buffer.length} 字节`));

        // 反序列化交易
        const transaction = Transaction.from(buffer);
        console.log(chalk.green(`✅ Buy交易解析成功`));
        console.log(chalk.blue(`📊 指令数量: ${transaction.instructions.length}`));

        // 分析每个指令
        transaction.instructions.forEach((instruction, index) => {
            console.log(chalk.blue.bold(`\n📋 指令 ${index + 1}:`));
            console.log(chalk.green(`程序ID: ${instruction.programId.toString()}`));
            console.log(chalk.green(`账户数量: ${instruction.keys.length}`));
            console.log(chalk.green(`数据长度: ${instruction.data.length} 字节`));
            
            if (instruction.programId.toString() === 'PPP5a1E95YMFv8Rmhvc7PBW6SSfHfvNKfAz7988xvB3') {
                console.log(chalk.yellow.bold('🎯 这是PPP程序指令!'));
                
                // 分析指令数据
                console.log(chalk.blue('\n🔍 指令数据分析:'));
                console.log(chalk.gray(`十六进制: ${instruction.data.toString('hex')}`));
                
                // 分析discriminator
                if (instruction.data.length >= 8) {
                    const discriminator = instruction.data.slice(0, 8);
                    console.log(chalk.yellow(`\n🎯 Buy Discriminator (前8字节): ${discriminator.toString('hex')}`));
                    
                    // 与之前的mint discriminator对比
                    const mintDiscriminator = '3339e12fb69289a6';
                    const buyDiscriminator = discriminator.toString('hex');
                    
                    console.log(chalk.blue('\n📊 Discriminator对比:'));
                    console.log(chalk.red(`❌ Mint: ${mintDiscriminator}`));
                    console.log(chalk.green(`✅ Buy:  ${buyDiscriminator}`));
                    
                    if (buyDiscriminator !== mintDiscriminator) {
                        console.log(chalk.green('🎉 找到了正确的Buy discriminator!'));
                    }
                    
                    // 分析剩余数据
                    if (instruction.data.length > 8) {
                        console.log(chalk.blue('\n💰 查找金额数据:'));
                        const remainingData = instruction.data.slice(8);
                        console.log(chalk.gray(`剩余数据: ${remainingData.toString('hex')}`));
                        
                        // 查找可能的金额
                        for (let i = 0; i <= remainingData.length - 8; i++) {
                            try {
                                const value = remainingData.readBigUInt64LE(i);
                                if (value > 1000000 && value < 10000000000) { // 0.001 到 10 SOL
                                    const solValue = Number(value) / 1000000000;
                                    console.log(chalk.green(`  位置 ${i + 8}: ${value} lamports (${solValue.toFixed(6)} SOL)`));
                                }
                            } catch (e) {}
                        }
                    }
                }
                
                // 分析账户
                console.log(chalk.blue('\n👥 账户分析:'));
                instruction.keys.forEach((key, keyIndex) => {
                    console.log(chalk.gray(`  账户 ${keyIndex}: ${key.pubkey.toString()}`));
                    console.log(chalk.gray(`    签名者: ${key.isSigner ? '是' : '否'}, 可写: ${key.isWritable ? '是' : '否'}`));
                });
            }
        });

        // 生成新的Buy指令模板
        const pppInstruction = transaction.instructions.find(
            inst => inst.programId.toString() === 'PPP5a1E95YMFv8Rmhvc7PBW6SSfHfvNKfAz7988xvB3'
        );

        if (pppInstruction) {
            console.log(chalk.blue.bold('\n🔧 生成Buy指令模板:'));
            
            const template = {
                programId: pppInstruction.programId.toString(),
                dataHex: pppInstruction.data.toString('hex'),
                dataLength: pppInstruction.data.length,
                discriminator: pppInstruction.data.slice(0, 8).toString('hex'),
                accounts: pppInstruction.keys.map((key, index) => ({
                    index,
                    pubkey: key.pubkey.toString(),
                    isSigner: key.isSigner,
                    isWritable: key.isWritable
                }))
            };

            console.log(chalk.green('✅ Buy指令模板:'));
            console.log(JSON.stringify(template, null, 2));

            // 保存模板到文件
            const fs = await import('fs');
            fs.default.writeFileSync('ppp-buy-instruction-template.json', JSON.stringify(template, null, 2));
            console.log(chalk.green('\n✅ Buy指令模板已保存到 ppp-buy-instruction-template.json'));
            
            // 关键信息总结
            console.log(chalk.blue.bold('\n📊 关键发现:'));
            console.log(chalk.green(`✅ 正确的Buy Discriminator: ${template.discriminator}`));
            console.log(chalk.green(`✅ 指令数据长度: ${template.dataLength} 字节`));
            console.log(chalk.green(`✅ 账户数量: ${template.accounts.length} 个`));
            
            const signers = template.accounts.filter(acc => acc.isSigner);
            console.log(chalk.green(`✅ 签名者数量: ${signers.length} 个`));
        }

    } catch (error) {
        console.error(chalk.red(`❌ 分析失败: ${error.message}`));
        console.error(chalk.gray(error.stack));
    }
}

// 运行分析
analyzeRealBuyTransaction().catch(console.error);
