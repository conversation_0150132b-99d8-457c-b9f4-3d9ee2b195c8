#!/usr/bin/env node

import { Transaction } from '@solana/web3.js';
import chalk from 'chalk';

/**
 * 深度分析交易数据中的价格信息
 * 钱包显示 -0.11 SOL，说明价格信息一定在某个地方
 */
async function analyzePriceDeeper() {
    console.log(chalk.blue.bold('🔍 深度分析交易中的价格信息\n'));
    console.log(chalk.yellow('钱包显示: -0.11 SOL，说明价格信息确实存在！\n'));

    // 用户提供的真实交易数据
    const transactionData = "AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAoYLKc67EPw55sWqYiwTA4T5gqg1a6RuEWZn0TTKXyY/zwVMJ93fKDfvXJ+S1DPaqjFkGH9kI8kX0Sp+JgAHHcz2UUmcRFp0nBBGJVh6+/FeNN9bVyEzm9gIOS8f6Vfg0XSYAhVPuDfVVWBXximUSYXqhNemkB/q8XuSpm6n/9GAd1rk9uhRkH+IvTqMDcV3TJ0ElyPleYp2p9FuqOjpjhZO3j4kewwSqd7NhvIrd5MFJhSbklJuolp1uUH4bXRKNNXtf9v2cKNuw8O7NzMCcw47S3ZWykXjlE+CsASGYuhHyy3fA6qtk5XrGeWresHmkh5hzC7TuZuh8u/BPS0Lr3s8bfQIlJUrAfjsr0/hsHw8RA/wHCMwVrvFAc6pkU/Vepp0E4fdhhGEc8Yrj+nEDZLLF215BpJq91Yfvu1gkOvJrnp1UmEjwHa9rZ1VKTcE+uUUUkcgoxNPNY9MleWOy4+2PeNVozGo0Pzota5SliFHAE8PcXZZ3wT0WDk8SBgCHom8q27pT/4Jyjxq+Ub0RJTwu0Fx8L4qPdMxx++UQmdPIAKmQ2GVn5m94O0bquBY9KJknDpmGMQuDD0fZUkPDXoZgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYNzKytmF6srPaYrkS8Cg8XVY7nAtIOQjYGl5bI60gzmMlyWPTiSJ8bs9ECkUjg2DC1oTmdr/EIQEjnvY2+n4WakqWotPKVlShCVQqpP9W5W1rOao65IMk5QuQ2kMIOxzsyE/uov5yH+pHkeBlijDg+AL6n6Yx6A+A7oQac/D9vPrANn1spK0IUrH0De01vBkULlkYA3zcwUrtehPL46aZwW8FW2DBFnM6lG7gdIDO9TKvFnVh25d/A166eylFBgiBpuIV/6rgYT7aH9jRhjANdrEOdwa6ztVmKDwAAAAAAEGp9UXGSxcUSGMyUw9SvF/WNruCJuh/UTj29mKAAAAAAbd9uHXZaGT2cvhRs7reawctIXtX1s3kTqM9YV+/wCpy5KiY7Yljdkc5+d6QhV7QZkl+9mqd5arzXzEQhvuWKMBFBsRAAESEwwVDwkLBw0ECggCFxcXEA4WBQMVCwYIMznhL7aSiaY=";

    try {
        // 解码Base64交易数据
        const buffer = Buffer.from(transactionData, 'base64');
        console.log(chalk.green(`✅ 交易数据长度: ${buffer.length} 字节`));

        // 反序列化交易
        const transaction = Transaction.from(buffer);
        console.log(chalk.green(`✅ 交易解析成功`));
        console.log(chalk.blue(`📊 指令数量: ${transaction.instructions.length}`));

        // 0.11 SOL = 110,000,000 lamports
        const targetAmount = 110000000;
        console.log(chalk.yellow(`🎯 目标金额: ${targetAmount} lamports (0.11 SOL)\n`));

        // 分析每个指令
        transaction.instructions.forEach((instruction, index) => {
            console.log(chalk.blue.bold(`\n📋 指令 ${index + 1}:`));
            console.log(chalk.green(`程序ID: ${instruction.programId.toString()}`));
            console.log(chalk.green(`账户数量: ${instruction.keys.length}`));
            console.log(chalk.green(`数据长度: ${instruction.data.length} 字节`));
            
            if (instruction.data.length > 0) {
                console.log(chalk.gray(`数据 (hex): ${instruction.data.toString('hex')}`));
                
                // 在指令数据中查找0.11 SOL (110,000,000 lamports)
                console.log(chalk.blue('\n🔍 查找 0.11 SOL (110,000,000 lamports):'));
                
                let found = false;
                
                // 检查8字节小端序
                for (let i = 0; i <= instruction.data.length - 8; i++) {
                    try {
                        const value = instruction.data.readBigUInt64LE(i);
                        if (value == targetAmount) {
                            console.log(chalk.green(`  ✅ 找到! 位置 ${i}: ${value} lamports (小端序)`));
                            found = true;
                        }
                    } catch (e) {}
                }
                
                // 检查8字节大端序
                for (let i = 0; i <= instruction.data.length - 8; i++) {
                    try {
                        const value = instruction.data.readBigUInt64BE(i);
                        if (value == targetAmount) {
                            console.log(chalk.green(`  ✅ 找到! 位置 ${i}: ${value} lamports (大端序)`));
                            found = true;
                        }
                    } catch (e) {}
                }
                
                // 检查4字节小端序
                for (let i = 0; i <= instruction.data.length - 4; i++) {
                    try {
                        const value = instruction.data.readUInt32LE(i);
                        if (value == targetAmount) {
                            console.log(chalk.green(`  ✅ 找到! 位置 ${i}: ${value} lamports (4字节小端序)`));
                            found = true;
                        }
                    } catch (e) {}
                }
                
                // 检查4字节大端序
                for (let i = 0; i <= instruction.data.length - 4; i++) {
                    try {
                        const value = instruction.data.readUInt32BE(i);
                        if (value == targetAmount) {
                            console.log(chalk.green(`  ✅ 找到! 位置 ${i}: ${value} lamports (4字节大端序)`));
                            found = true;
                        }
                    } catch (e) {}
                }
                
                // 查找接近的值 (可能有精度差异)
                console.log(chalk.blue('\n🔍 查找接近的金额:'));
                for (let i = 0; i <= instruction.data.length - 8; i++) {
                    try {
                        const value = instruction.data.readBigUInt64LE(i);
                        const diff = Math.abs(Number(value) - targetAmount);
                        
                        // 如果差异小于1000 lamports (0.000001 SOL)
                        if (diff < 1000 && value > 1000000 && value < 1000000000) {
                            const solValue = Number(value) / 1000000000;
                            console.log(chalk.yellow(`  📍 接近值 位置 ${i}: ${value} lamports (${solValue.toFixed(6)} SOL)`));
                        }
                    } catch (e) {}
                }
                
                if (!found) {
                    console.log(chalk.red('  ❌ 未在此指令中找到 0.11 SOL'));
                }
            }
            
            // 分析账户 (价格可能在账户数据中)
            console.log(chalk.blue('\n👥 账户分析:'));
            instruction.keys.forEach((key, keyIndex) => {
                console.log(chalk.gray(`  账户 ${keyIndex}: ${key.pubkey.toString()}`));
                console.log(chalk.gray(`    签名者: ${key.isSigner ? '是' : '否'}, 可写: ${key.isWritable ? '是' : '否'}`));
            });
        });

        // 检查交易消息的其他部分
        console.log(chalk.blue.bold('\n🔍 检查交易的其他部分:'));
        
        // 检查原始buffer中是否有0.11 SOL
        console.log(chalk.blue('\n📊 在整个交易buffer中查找 0.11 SOL:'));
        
        let foundInBuffer = false;
        
        // 8字节搜索
        for (let i = 0; i <= buffer.length - 8; i++) {
            try {
                const value = buffer.readBigUInt64LE(i);
                if (value == targetAmount) {
                    console.log(chalk.green(`✅ 在buffer位置 ${i} 找到: ${value} lamports (小端序)`));
                    foundInBuffer = true;
                    
                    // 显示周围的数据
                    const start = Math.max(0, i - 16);
                    const end = Math.min(buffer.length, i + 24);
                    const context = buffer.slice(start, end);
                    console.log(chalk.gray(`   上下文: ${context.toString('hex')}`));
                }
            } catch (e) {}
        }
        
        // 4字节搜索
        for (let i = 0; i <= buffer.length - 4; i++) {
            try {
                const value = buffer.readUInt32LE(i);
                if (value == targetAmount) {
                    console.log(chalk.green(`✅ 在buffer位置 ${i} 找到: ${value} lamports (4字节小端序)`));
                    foundInBuffer = true;
                    
                    // 显示周围的数据
                    const start = Math.max(0, i - 8);
                    const end = Math.min(buffer.length, i + 12);
                    const context = buffer.slice(start, end);
                    console.log(chalk.gray(`   上下文: ${context.toString('hex')}`));
                }
            } catch (e) {}
        }

        if (!foundInBuffer) {
            console.log(chalk.red('❌ 在整个交易buffer中未找到 0.11 SOL'));
        }

        // 总结
        console.log(chalk.blue.bold('\n📊 分析结论:'));
        if (foundInBuffer) {
            console.log(chalk.green('✅ 在交易数据中找到了 0.11 SOL!'));
            console.log(chalk.blue('💡 这证明价格信息确实包含在交易中'));
        } else {
            console.log(chalk.yellow('⚠️  未在交易数据中找到明确的 0.11 SOL'));
            console.log(chalk.blue('💡 可能的原因:'));
            console.log(chalk.gray('1. 价格以不同的格式存储 (如浮点数)'));
            console.log(chalk.gray('2. 价格在账户数据中，而不是指令数据中'));
            console.log(chalk.gray('3. 价格通过其他方式计算 (如比例、百分比等)'));
            console.log(chalk.gray('4. 钱包显示的金额是模拟计算的结果'));
        }

    } catch (error) {
        console.error(chalk.red(`❌ 分析失败: ${error.message}`));
        console.error(chalk.gray(error.stack));
    }
}

// 运行分析
analyzePriceDeeper().catch(console.error);
