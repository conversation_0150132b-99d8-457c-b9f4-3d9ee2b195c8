#!/usr/bin/env node

import { Transaction } from '@solana/web3.js';
import chalk from 'chalk';

// 真正的buy交易数据
const buyTransactionData = "AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAgULKc67EPw55sWqYiwTA4T5gqg1a6RuEWZn0TTKXyY/zwoAMSQ/YLSGNl6IAGJlp2QHHN6SQum434HAlKaKdrM3Tcyey7jFG9oo5d8gneoEi9axb/nSzAUqrqv4AnH4ChiOdt8W7gJVHjKh1UF7v6Azvm7VL1xWmzEEA5VUTG3OMABLce+dHFL/a925J3dv9e50H61miO8c5Jc4EFcyo/ex13xG0EHy68lclWoV2B3y2Ln0ZSBflATXEs2wVafd65qtf9v2cKNuw8O7NzMCcw47S3ZWykXjlE+CsASGYuhHyzMOxBYtswOAif5tIIgkKrqB73QV9cD24C1q8DdkbxJScYZSrsc44lZqblp2/NR/n3oz1Sy3PqyQ2k1cnnqKYC61vXVRZbfq9d/EyHYkqVNHDUucFpR861x7VHVKpFiR0ztObibFSKoB9JIv1+T4l9al60SyUgJgJVASPIIXieC7AuNw0A/CO6h58hRCLX4NxLsAYATMnNis8o3ZNClvc3oAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABK2QbdSssl2FYiG4vmZxMcCvWsw918ySPX08i1yJ0ZWakqWotPKVlShCVQqpP9W5W1rOao65IMk5QuQ2kMIOxzsyE/uov5yH+pHkeBlijDg+AL6n6Yx6A+A7oQac/D9vPrANn1spK0IUrH0De01vBkULlkYA3zcwUrtehPL46aZwW8FW2DBFnM6lG7gdIDO9TKvFnVh25d/A166eylFBgiBpuIV/6rgYT7aH9jRhjANdrEOdwa6ztVmKDwAAAAAAEG3fbh12Whk9nL4UbO63msHLSF7V9bN5E6jPWFfv8Aqf/XWppZ+KQqfrIsMl20octTcjru73lViw/POvCbHxj0AREXDgAQDwILCQQDExMSDQcMEwUIARILCgYIZgY9EgHa6+o=";

console.log(chalk.blue.bold('🔍 分析真实Buy交易的账户结构\n'));

try {
    const buffer = Buffer.from(buyTransactionData, 'base64');
    const transaction = Transaction.from(buffer);
    
    const pppInstruction = transaction.instructions.find(
        inst => inst.programId.toString() === 'PPP5a1E95YMFv8Rmhvc7PBW6SSfHfvNKfAz7988xvB3'
    );
    
    if (pppInstruction) {
        console.log(chalk.green(`✅ 找到PPP Buy指令，账户数量: ${pppInstruction.keys.length}`));
        
        console.log(chalk.blue.bold('\n📋 真实Buy交易的账户列表:'));
        pppInstruction.keys.forEach((key, index) => {
            const signerText = key.isSigner ? '🔑' : '  ';
            const writableText = key.isWritable ? '✏️ ' : '👁️ ';
            console.log(chalk.gray(`${index.toString().padStart(2)}: ${signerText} ${writableText} ${key.pubkey.toString()}`));
        });
        
        console.log(chalk.blue.bold('\n🔧 生成正确的账户配置代码:'));
        console.log(chalk.yellow('const keys = ['));
        
        pppInstruction.keys.forEach((key, index) => {
            const comment = index === 1 ? ' // 买家 (唯一签名者)' : 
                           index === 0 ? ' // Raydium AMM' :
                           index === pppInstruction.keys.length - 1 ? ' // 最后一个账户' : '';
            
            if (index === 1) {
                // 买家账户使用动态参数
                console.log(chalk.yellow(`    { pubkey: buyer, isSigner: ${key.isSigner}, isWritable: ${key.isWritable} },${comment}`));
            } else if (index === 17 || index === 18) {
                // NFT和拥有者相关账户
                const paramName = index === 17 ? 'nftPubkey' : 'previousOwner';
                console.log(chalk.yellow(`    { pubkey: ${paramName}, isSigner: ${key.isSigner}, isWritable: ${key.isWritable} },${comment}`));
            } else {
                // 固定账户
                console.log(chalk.yellow(`    { pubkey: new PublicKey('${key.pubkey.toString()}'), isSigner: ${key.isSigner}, isWritable: ${key.isWritable} },${comment}`));
            }
        });
        
        console.log(chalk.yellow('];'));
        
        // 分析签名者
        const signers = pppInstruction.keys.filter(key => key.isSigner);
        console.log(chalk.blue.bold('\n👤 签名者分析:'));
        console.log(chalk.green(`签名者数量: ${signers.length}`));
        signers.forEach((signer, index) => {
            console.log(chalk.gray(`  签名者 ${index + 1}: ${signer.pubkey.toString()}`));
        });
        
        // 分析可写账户
        const writableAccounts = pppInstruction.keys.filter(key => key.isWritable);
        console.log(chalk.blue.bold('\n✏️  可写账户分析:'));
        console.log(chalk.green(`可写账户数量: ${writableAccounts.length}`));
        
        console.log(chalk.blue.bold('\n📊 与当前配置对比:'));
        console.log(chalk.red(`❌ 当前: 27个账户`));
        console.log(chalk.green(`✅ 正确: ${pppInstruction.keys.length}个账户`));
        console.log(chalk.blue('需要移除多余的4个账户'));
        
    } else {
        console.log(chalk.red('❌ 未找到PPP程序指令'));
    }
    
} catch (error) {
    console.error(chalk.red(`❌ 分析失败: ${error.message}`));
}

console.log(chalk.blue.bold('\n🎉 分析完成!'));
