#!/usr/bin/env node

import { Transaction } from '@solana/web3.js';
import chalk from 'chalk';

/**
 * 解析真实的PPP购买交易数据
 */
async function parseRealTransaction() {
    console.log(chalk.blue.bold('🔍 解析真实PPP购买交易\n'));

    // 从用户提供的真实交易数据
    const transactionData = "AQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAoYLKc67EPw55sWqYiwTA4T5gqg1a6RuEWZn0TTKXyY/zwVMJ93fKDfvXJ+S1DPaqjFkGH9kI8kX0Sp+JgAHHcz2UUmcRFp0nBBGJVh6+/FeNN9bVyEzm9gIOS8f6Vfg0XSYAhVPuDfVVWBXximUSYXqhNemkB/q8XuSpm6n/9GAd1rk9uhRkH+IvTqMDcV3TJ0ElyPleYp2p9FuqOjpjhZO3j4kewwSqd7NhvIrd5MFJhSbklJuolp1uUH4bXRKNNXtf9v2cKNuw8O7NzMCcw47S3ZWykXjlE+CsASGYuhHyy3fA6qtk5XrGeWresHmkh5hzC7TuZuh8u/BPS0Lr3s8bfQIlJUrAfjsr0/hsHw8RA/wHCMwVrvFAc6pkU/Vepp0E4fdhhGEc8Yrj+nEDZLLF215BpJq91Yfvu1gkOvJrnp1UmEjwHa9rZ1VKTcE+uUUUkcgoxNPNY9MleWOy4+2PeNVozGo0Pzota5SliFHAE8PcXZZ3wT0WDk8SBgCHom8q27pT/4Jyjxq+Ub0RJTwu0Fx8L4qPdMxx++UQmdPIAKmQ2GVn5m94O0bquBY9KJknDpmGMQuDD0fZUkPDXoZgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYNzKytmF6srPaYrkS8Cg8XVY7nAtIOQjYGl5bI60gzmMlyWPTiSJ8bs9ECkUjg2DC1oTmdr/EIQEjnvY2+n4WakqWotPKVlShCVQqpP9W5W1rOao65IMk5QuQ2kMIOxzsyE/uov5yH+pHkeBlijDg+AL6n6Yx6A+A7oQac/D9vPrANn1spK0IUrH0De01vBkULlkYA3zcwUrtehPL46aZwW8FW2DBFnM6lG7gdIDO9TKvFnVh25d/A166eylFBgiBpuIV/6rgYT7aH9jRhjANdrEOdwa6ztVmKDwAAAAAAEGp9UXGSxcUSGMyUw9SvF/WNruCJuh/UTj29mKAAAAAAbd9uHXZaGT2cvhRs7reawctIXtX1s3kTqM9YV+/wCpy5KiY7Yljdkc5+d6QhV7QZkl+9mqd5arzXzEQhvuWKMBFBsRAAESEwwVDwkLBw0ECggCFxcXEA4WBQMVCwYIMznhL7aSiaY=";

    try {
        // 解码Base64交易数据
        const buffer = Buffer.from(transactionData, 'base64');
        console.log(chalk.green(`✅ 交易数据长度: ${buffer.length} 字节`));

        // 反序列化交易
        const transaction = Transaction.from(buffer);
        console.log(chalk.green(`✅ 交易解析成功`));
        console.log(chalk.blue(`📊 指令数量: ${transaction.instructions.length}`));

        // 分析每个指令
        transaction.instructions.forEach((instruction, index) => {
            console.log(chalk.blue.bold(`\n📋 指令 ${index + 1}:`));
            console.log(chalk.green(`程序ID: ${instruction.programId.toString()}`));
            console.log(chalk.green(`账户数量: ${instruction.keys.length}`));
            console.log(chalk.green(`数据长度: ${instruction.data.length} 字节`));
            
            if (instruction.programId.toString() === 'PPP5a1E95YMFv8Rmhvc7PBW6SSfHfvNKfAz7988xvB3') {
                console.log(chalk.yellow.bold('🎯 这是PPP程序指令!'));
                
                // 分析指令数据
                console.log(chalk.blue('\n🔍 指令数据分析:'));
                console.log(chalk.gray(`十六进制: ${instruction.data.toString('hex')}`));
                
                // 分析前16字节
                console.log(chalk.blue('\n📊 前16字节:'));
                for (let i = 0; i < Math.min(16, instruction.data.length); i++) {
                    console.log(chalk.gray(`  字节 ${i}: ${instruction.data[i]} (0x${instruction.data[i].toString(16).padStart(2, '0')})`));
                }
                
                // 查找可能的discriminator
                if (instruction.data.length >= 8) {
                    const discriminator = instruction.data.slice(0, 8);
                    console.log(chalk.yellow(`\n🎯 Discriminator (前8字节): ${discriminator.toString('hex')}`));
                    
                    // 查找金额数据
                    console.log(chalk.blue('\n💰 查找金额数据:'));
                    for (let i = 8; i <= instruction.data.length - 8; i++) {
                        try {
                            const value = instruction.data.readBigUInt64LE(i);
                            if (value > 1000000 && value < 10000000000) { // 0.001 到 10 SOL 范围
                                console.log(chalk.green(`  位置 ${i}: ${value} lamports (${Number(value) / 1000000000} SOL)`));
                            }
                        } catch (e) {
                            // 忽略读取错误
                        }
                    }
                }
                
                // 分析账户
                console.log(chalk.blue('\n👥 账户分析:'));
                instruction.keys.forEach((key, keyIndex) => {
                    console.log(chalk.gray(`  账户 ${keyIndex}: ${key.pubkey.toString()}`));
                    console.log(chalk.gray(`    签名者: ${key.isSigner ? '是' : '否'}, 可写: ${key.isWritable ? '是' : '否'}`));
                });
            }
        });

        // 生成新的指令模板
        const pppInstruction = transaction.instructions.find(
            inst => inst.programId.toString() === 'PPP5a1E95YMFv8Rmhvc7PBW6SSfHfvNKfAz7988xvB3'
        );

        if (pppInstruction) {
            console.log(chalk.blue.bold('\n🔧 生成新的指令模板:'));
            
            const template = {
                programId: pppInstruction.programId.toString(),
                dataHex: pppInstruction.data.toString('hex'),
                dataLength: pppInstruction.data.length,
                accounts: pppInstruction.keys.map((key, index) => ({
                    index,
                    pubkey: key.pubkey.toString(),
                    isSigner: key.isSigner,
                    isWritable: key.isWritable
                }))
            };

            console.log(chalk.green('✅ 指令模板:'));
            console.log(JSON.stringify(template, null, 2));

            // 保存模板到文件
            const fs = await import('fs');
            fs.default.writeFileSync('ppp-instruction-template.json', JSON.stringify(template, null, 2));
            console.log(chalk.green('\n✅ 指令模板已保存到 ppp-instruction-template.json'));
        }

    } catch (error) {
        console.error(chalk.red(`❌ 解析失败: ${error.message}`));
        console.error(chalk.gray(error.stack));
    }
}

// 运行解析
parseRealTransaction().catch(console.error);
