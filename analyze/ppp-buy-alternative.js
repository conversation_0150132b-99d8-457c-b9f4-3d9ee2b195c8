#!/usr/bin/env node

import { Connection, PublicKey, Transaction, TransactionInstruction, SystemProgram } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID } from '@solana/spl-token';
import BN from 'bn.js';
import chalk from 'chalk';

/**
 * 替代的PPP Buy实现
 * 尝试不同的方法来构建正确的指令
 */
class PPPBuyAlternative {
    constructor(connection, wallet) {
        this.connection = connection;
        this.wallet = wallet;
        this.programId = new PublicKey('PPP5a1E95YMFv8Rmhvc7PBW6SSfHfvNKfAz7988xvB3');
    }

    /**
     * 方法1: 尝试使用简化的指令格式
     */
    async createSimpleBuyInstruction(params) {
        const { nftPubkey, buyer, previousOwner, amount, accounts } = params;

        console.log(chalk.blue('🔧 尝试方法1: 简化指令格式'));

        // 尝试使用更简单的指令格式
        const instructionData = Buffer.alloc(16);
        
        // 可能的buy指令discriminator (尝试不同的值)
        const possibleDiscriminators = [
            Buffer.from([0x66, 0x06, 0x3d, 0x12, 0x01, 0x00, 0x00, 0x00]), // "buy" 的hash
            Buffer.from([0x27, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]), // 原来的39
            Buffer.from([0xf3, 0x5b, 0x77, 0x43, 0xfd, 0x33, 0x5b, 0xe9]), // 从交易记录提取的
        ];

        for (let i = 0; i < possibleDiscriminators.length; i++) {
            console.log(chalk.gray(`   尝试discriminator ${i + 1}: ${possibleDiscriminators[i].toString('hex')}`));
            
            const data = Buffer.alloc(16);
            possibleDiscriminators[i].copy(data, 0);
            
            // 写入金额
            const amountBN = new BN(amount);
            amountBN.toArrayLike(Buffer, 'le', 8).copy(data, 8);

            const keys = accounts.map((account, index) => ({
                pubkey: new PublicKey(account),
                isSigner: index === 1, // 只有买家需要签名
                isWritable: true
            }));

            const instruction = new TransactionInstruction({
                keys,
                programId: this.programId,
                data
            });

            // 测试这个指令
            try {
                await this.testInstruction(instruction, `方法1-discriminator${i + 1}`);
                return instruction; // 如果成功，返回这个指令
            } catch (error) {
                console.log(chalk.red(`   ❌ discriminator ${i + 1} 失败: ${error.message}`));
            }
        }

        throw new Error('所有简化格式都失败了');
    }

    /**
     * 方法2: 尝试使用原始交易的完整数据但修改关键参数
     */
    async createModifiedOriginalInstruction(params) {
        const { nftPubkey, buyer, previousOwner, amount, accounts } = params;

        console.log(chalk.blue('🔧 尝试方法2: 修改原始指令数据'));

        // 原始指令数据
        const originalHex = "f35b7743fd335be90a99a2489c2b77533055a78cce27d568b1de238323c7b7c41f4f257cbb11d451ffeefdb73ff77e16eecacae930a1dca9f247471b4699cc849379391f605fe7724600000000000000f30100000000000004000000000000007710d20c3fc7c136879e77c7c44fae14b4d83793824e29f2d93f6a7e0e52d78e50d40a94373428a47de0657ea29c1f1006bd5bc73697fb74b0d05440e6bb003c5032a22b0000000000e40b540200000039bf74680000000003000000427579";
        
        const instructionData = Buffer.from(originalHex, 'hex');

        // 尝试修改不同的参数位置
        const modifications = [
            { position: 160, value: amount, description: '金额位置160' },
            { position: 152, value: amount, description: '金额位置152' },
            { position: 168, value: amount, description: '金额位置168' },
        ];

        for (const mod of modifications) {
            console.log(chalk.gray(`   尝试修改 ${mod.description}`));
            
            const data = Buffer.from(instructionData);
            const amountBN = new BN(mod.value);
            amountBN.toArrayLike(Buffer, 'le', 8).copy(data, mod.position);

            const keys = accounts.map((account, index) => ({
                pubkey: new PublicKey(account),
                isSigner: index === 1, // 只有买家需要签名
                isWritable: true
            }));

            const instruction = new TransactionInstruction({
                keys,
                programId: this.programId,
                data
            });

            try {
                await this.testInstruction(instruction, `方法2-${mod.description}`);
                return instruction;
            } catch (error) {
                console.log(chalk.red(`   ❌ ${mod.description} 失败: ${error.message}`));
            }
        }

        throw new Error('所有修改都失败了');
    }

    /**
     * 方法3: 尝试不同的账户权限配置
     */
    async createDifferentPermissionsInstruction(params) {
        const { nftPubkey, buyer, previousOwner, amount, accounts } = params;

        console.log(chalk.blue('🔧 尝试方法3: 不同的账户权限配置'));

        const instructionData = Buffer.alloc(16);
        Buffer.from([0x66, 0x06, 0x3d, 0x12, 0x01, 0x00, 0x00, 0x00]).copy(instructionData, 0);
        const amountBN = new BN(amount);
        amountBN.toArrayLike(Buffer, 'le', 8).copy(instructionData, 8);

        // 尝试不同的权限配置
        const permissionConfigs = [
            {
                name: '配置1: 只有买家签名，所有账户可写',
                keys: accounts.map((account, index) => ({
                    pubkey: new PublicKey(account),
                    isSigner: index === 1,
                    isWritable: true
                }))
            },
            {
                name: '配置2: 买家和拥有者签名',
                keys: accounts.map((account, index) => ({
                    pubkey: new PublicKey(account),
                    isSigner: index === 1 || index === 18, // 买家和拥有者
                    isWritable: true
                }))
            },
            {
                name: '配置3: 部分账户只读',
                keys: accounts.map((account, index) => ({
                    pubkey: new PublicKey(account),
                    isSigner: index === 1,
                    isWritable: ![9, 10, 11, 14, 15, 19].includes(index) // 程序账户只读
                }))
            }
        ];

        for (const config of permissionConfigs) {
            console.log(chalk.gray(`   尝试 ${config.name}`));

            const instruction = new TransactionInstruction({
                keys: config.keys,
                programId: this.programId,
                data: instructionData
            });

            try {
                await this.testInstruction(instruction, config.name);
                return instruction;
            } catch (error) {
                console.log(chalk.red(`   ❌ ${config.name} 失败: ${error.message}`));
            }
        }

        throw new Error('所有权限配置都失败了');
    }

    /**
     * 测试指令是否有效
     */
    async testInstruction(instruction, methodName) {
        console.log(chalk.gray(`   测试指令: ${methodName}`));

        const transaction = new Transaction();
        transaction.add(instruction);

        const { blockhash } = await this.connection.getLatestBlockhash();
        transaction.recentBlockhash = blockhash;
        transaction.feePayer = this.wallet.publicKey;

        // 只进行模拟，不实际发送
        try {
            const simulation = await this.connection.simulateTransaction(transaction);
            
            if (simulation.value.err) {
                throw new Error(`模拟失败: ${JSON.stringify(simulation.value.err)}`);
            }

            console.log(chalk.green(`   ✅ ${methodName} 模拟成功!`));
            console.log(chalk.gray(`   日志: ${simulation.value.logs?.slice(0, 3).join(', ')}...`));
            
            return true;
        } catch (error) {
            throw new Error(`模拟失败: ${error.message}`);
        }
    }

    /**
     * 尝试所有方法
     */
    async findWorkingInstruction(params) {
        console.log(chalk.blue.bold('🔍 尝试找到有效的PPP Buy指令格式\n'));

        const methods = [
            this.createSimpleBuyInstruction.bind(this),
            this.createModifiedOriginalInstruction.bind(this),
            this.createDifferentPermissionsInstruction.bind(this)
        ];

        for (let i = 0; i < methods.length; i++) {
            try {
                console.log(chalk.blue(`\n📋 尝试方法 ${i + 1}:`));
                const instruction = await methods[i](params);
                
                console.log(chalk.green.bold(`\n🎉 找到有效的指令格式! (方法 ${i + 1})`));
                return instruction;
                
            } catch (error) {
                console.log(chalk.red(`❌ 方法 ${i + 1} 失败: ${error.message}`));
            }
        }

        throw new Error('所有方法都失败了，无法找到有效的指令格式');
    }
}

export { PPPBuyAlternative };
