#!/usr/bin/env node

import fetch from 'node-fetch';
import chalk from 'chalk';
import inquirer from 'inquirer';
import fs from 'fs';

class PppFunProjectsAPI {
  constructor() {
    this.baseUrl = 'https://api.ppp.fun';
  }

  // 获取项目列表
  async getProjects(options = {}) {
    const {
      limit = 20,
      page = 1,
      keyword = '',
      sort = ''
    } = options;

    try {
      const url = `${this.baseUrl}/projects?limit=${limit}&page=${page}&keyword=${encodeURIComponent(keyword)}&sort=${sort}`;
      console.log(chalk.blue(`正在请求: ${url}`));

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'PPP-Fun-Projects-Tool/1.0.0'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error('API 返回失败状态');
      }

      return data;
    } catch (error) {
      console.error(chalk.red(`请求失败: ${error.message}`));
      throw error;
    }
  }

  // 提取项目基本信息
  extractProjectInfo(project) {
    return {
      // 基本标识
      project_pubkey: project.project_pubkey,
      mint_pubkey: project.mint_pubkey,
      project_mint: project.project_mint,
      
      // 项目信息
      project_name: project.project_name,
      project_desc: project.project_desc,
      token_symbol: project.token_symbol,
      
      // 代币信息
      init_token_supply: project.init_token_supply,
      max_token_supply: project.max_token_supply,
      circulation_supply: project.circulation_supply,
      
      // NFT 信息
      nft_name: project.nft_name,
      nft_id: project.nft_id,
      nft_issue_count: project.nft_issue_count,
      nft_burn_count: project.nft_burn_count,
      
      // 市场数据
      market_cap: project.market_cap,
      fdv: project.fdv,
      volume_24h: project.volume_24h,
      tx_24h: project.tx_24h,
      
      // 媒体资源
      image_url: project.image_url,
      nft_image_url: project.nft_image_url,
      token_uri: project.token_uri,
      
      // 社交链接
      website: project.website,
      twitter: project.twitter,
      telegram: project.telegram,
      discord: project.discord,
      
      // 时间信息
      create_time: project.create_time,
      updated_at: project.updated_at,
      
      // 创建者信息
      creator_pubkey: project.creator_pubkey,
      authority_pubkey: project.authority_pubkey
    };
  }

  // 显示项目信息
  displayProject(project, index) {
    const info = this.extractProjectInfo(project);
    
    console.log(chalk.blue.bold(`\n📊 项目 ${index + 1}: ${info.project_name}`));
    console.log('═'.repeat(60));
    
    console.log(chalk.green.bold('🏷️  基本信息:'));
    console.log(`项目名称: ${info.project_name}`);
    console.log(`项目描述: ${info.project_desc}`);
    console.log(`代币符号: ${info.token_symbol}`);
    console.log(`NFT 名称: ${info.nft_name}`);
    
    console.log(chalk.green.bold('\n🔑 合约地址:'));
    console.log(`项目地址: ${info.project_pubkey}`);
    console.log(`代币地址: ${info.mint_pubkey}`);
    console.log(`创建者: ${info.creator_pubkey}`);
    
    console.log(chalk.green.bold('\n💰 代币信息:'));
    console.log(`初始供应量: ${this.formatNumber(info.init_token_supply)}`);
    console.log(`最大供应量: ${this.formatNumber(info.max_token_supply)}`);
    console.log(`流通供应量: ${this.formatNumber(info.circulation_supply)}`);
    
    console.log(chalk.green.bold('\n🎨 NFT 信息:'));
    console.log(`NFT ID: ${info.nft_id}`);
    console.log(`已发行: ${info.nft_issue_count}`);
    console.log(`已销毁: ${info.nft_burn_count}`);
    
    console.log(chalk.green.bold('\n📈 市场数据:'));
    console.log(`市值: $${this.formatNumber(info.market_cap)}`);
    console.log(`FDV: $${this.formatNumber(info.fdv)}`);
    console.log(`24h 交易量: $${this.formatNumber(info.volume_24h)}`);
    console.log(`24h 交易次数: ${info.tx_24h}`);
    
    if (info.website || info.twitter || info.telegram || info.discord) {
      console.log(chalk.green.bold('\n🔗 社交链接:'));
      if (info.website) console.log(`官网: ${info.website}`);
      if (info.twitter) console.log(`Twitter: ${info.twitter}`);
      if (info.telegram) console.log(`Telegram: ${info.telegram}`);
      if (info.discord) console.log(`Discord: ${info.discord}`);
    }
    
    console.log(chalk.green.bold('\n🖼️  媒体资源:'));
    if (info.image_url) console.log(`项目图片: ${info.image_url}`);
    if (info.nft_image_url) console.log(`NFT 图片: ${info.nft_image_url}`);
    
    console.log(chalk.green.bold('\n⏰ 时间信息:'));
    console.log(`创建时间: ${new Date(info.create_time).toLocaleString()}`);
    console.log(`更新时间: ${new Date(info.updated_at).toLocaleString()}`);
  }

  // 格式化数字
  formatNumber(num) {
    if (!num) return '0';
    return new Intl.NumberFormat().format(num);
  }

  // 保存项目数据
  saveProjectsData(projects, filename) {
    try {
      const extractedData = projects.map(project => this.extractProjectInfo(project));
      fs.writeFileSync(filename, JSON.stringify(extractedData, null, 2));
      console.log(chalk.green(`✅ 数据已保存到: ${filename}`));
      return filename;
    } catch (error) {
      console.error(chalk.red(`保存失败: ${error.message}`));
      return null;
    }
  }

  // 保存为 CSV 格式
  saveProjectsCSV(projects, filename) {
    try {
      const extractedData = projects.map(project => this.extractProjectInfo(project));
      
      if (extractedData.length === 0) {
        throw new Error('没有数据可保存');
      }

      // CSV 头部
      const headers = Object.keys(extractedData[0]);
      const csvContent = [
        headers.join(','),
        ...extractedData.map(row => 
          headers.map(header => `"${row[header] || ''}"`).join(',')
        )
      ].join('\n');

      fs.writeFileSync(filename, csvContent);
      console.log(chalk.green(`✅ CSV 数据已保存到: ${filename}`));
      return filename;
    } catch (error) {
      console.error(chalk.red(`保存 CSV 失败: ${error.message}`));
      return null;
    }
  }

  // 搜索项目
  async searchProjects(keyword, limit = 20) {
    console.log(chalk.blue(`正在搜索项目: "${keyword}"`));
    return await this.getProjects({ keyword, limit });
  }

  // 获取热门项目（按交易量排序）
  async getHotProjects(limit = 20) {
    console.log(chalk.blue('正在获取热门项目...'));
    return await this.getProjects({ limit, sort: 'volume_24h_desc' });
  }

  // 获取最新项目
  async getLatestProjects(limit = 20) {
    console.log(chalk.blue('正在获取最新项目...'));
    return await this.getProjects({ limit, sort: 'create_time_desc' });
  }
}

// 主函数
async function main() {
  console.log(chalk.blue.bold('🎯 PPP.Fun 项目信息获取工具\n'));
  
  const api = new PppFunProjectsAPI();
  
  const { action } = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: '选择操作:',
      choices: [
        { name: '📋 获取项目列表', value: 'list' },
        { name: '🔍 搜索项目', value: 'search' },
        { name: '🔥 获取热门项目', value: 'hot' },
        { name: '🆕 获取最新项目', value: 'latest' },
        { name: '⚙️  自定义查询', value: 'custom' }
      ]
    }
  ]);

  let result;

  try {
    switch (action) {
      case 'list':
        const { limit } = await inquirer.prompt([
          {
            type: 'number',
            name: 'limit',
            message: '获取项目数量:',
            default: 20,
            validate: (value) => value > 0 && value <= 100 || '请输入 1-100 之间的数字'
          }
        ]);
        result = await api.getProjects({ limit });
        break;

      case 'search':
        const { keyword } = await inquirer.prompt([
          {
            type: 'input',
            name: 'keyword',
            message: '输入搜索关键词:',
            validate: (value) => value.length > 0 || '请输入搜索关键词'
          }
        ]);
        result = await api.searchProjects(keyword);
        break;

      case 'hot':
        result = await api.getHotProjects();
        break;

      case 'latest':
        result = await api.getLatestProjects();
        break;

      case 'custom':
        const customOptions = await inquirer.prompt([
          {
            type: 'number',
            name: 'limit',
            message: '获取数量:',
            default: 20
          },
          {
            type: 'number',
            name: 'page',
            message: '页码:',
            default: 1
          },
          {
            type: 'input',
            name: 'keyword',
            message: '搜索关键词 (可选):',
            default: ''
          },
          {
            type: 'input',
            name: 'sort',
            message: '排序方式 (可选):',
            default: ''
          }
        ]);
        result = await api.getProjects(customOptions);
        break;
    }

    if (result && result.data && result.data.length > 0) {
      console.log(chalk.green.bold(`\n✅ 成功获取 ${result.data.length} 个项目:`));
      
      // 显示项目信息
      result.data.forEach((project, index) => {
        api.displayProject(project, index);
      });

      // 询问是否保存
      const { save } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'save',
          message: '是否保存数据到文件?',
          default: true
        }
      ]);

      if (save) {
        const { format } = await inquirer.prompt([
          {
            type: 'list',
            name: 'format',
            message: '选择保存格式:',
            choices: ['json', 'csv']
          }
        ]);

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `ppp-fun-projects-${timestamp}.${format}`;

        if (format === 'json') {
          api.saveProjectsData(result.data, filename);
        } else {
          api.saveProjectsCSV(result.data, filename);
        }
      }

    } else {
      console.log(chalk.yellow('未找到项目数据'));
    }

  } catch (error) {
    console.error(chalk.red(`操作失败: ${error.message}`));
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.log(chalk.red(`❌ 未捕获的异常: ${error.message}`));
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.log(chalk.red(`❌ 未处理的Promise拒绝: ${reason}`));
  process.exit(1);
});

// 启动
main().catch(console.error);
