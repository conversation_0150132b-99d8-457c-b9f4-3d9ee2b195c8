#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import inquirer from 'inquirer';

/**
 * 日志管理工具
 */
class LogManager {
    constructor() {
        this.logDir = './logs';
    }

    /**
     * 确保日志目录存在
     */
    ensureLogDir() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
            console.log(chalk.blue(`📁 创建日志目录: ${this.logDir}`));
        }
    }

    /**
     * 获取所有日志文件
     */
    getLogFiles() {
        this.ensureLogDir();
        
        try {
            const files = fs.readdirSync(this.logDir)
                .filter(file => file.endsWith('.json'))
                .map(file => {
                    const filePath = path.join(this.logDir, file);
                    const stats = fs.statSync(filePath);
                    return {
                        name: file,
                        path: filePath,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime
                    };
                })
                .sort((a, b) => b.modified - a.modified); // 按修改时间倒序

            return files;
        } catch (error) {
            console.error(chalk.red(`读取日志目录失败: ${error.message}`));
            return [];
        }
    }

    /**
     * 显示日志文件列表
     */
    displayLogFiles() {
        const files = this.getLogFiles();
        
        if (files.length === 0) {
            console.log(chalk.yellow('📂 日志目录为空'));
            return;
        }

        console.log(chalk.blue.bold(`\n📋 日志文件列表 (${files.length} 个文件):`));
        console.log('═'.repeat(80));

        files.forEach((file, index) => {
            const sizeKB = (file.size / 1024).toFixed(2);
            const dateStr = file.modified.toLocaleString();
            
            // 根据文件类型显示不同颜色
            let typeIcon = '📄';
            let typeColor = chalk.gray;
            
            if (file.name.includes('batch-purchase-summary')) {
                typeIcon = '📊';
                typeColor = chalk.green;
            } else if (file.name.includes('nft-purchase')) {
                typeIcon = '🎨';
                typeColor = chalk.cyan;
            } else if (file.name.includes('single-purchase')) {
                typeIcon = '🛒';
                typeColor = chalk.blue;
            }

            console.log(typeColor(`${typeIcon} ${index + 1}. ${file.name}`));
            console.log(chalk.gray(`   大小: ${sizeKB} KB | 修改时间: ${dateStr}`));
        });
    }

    /**
     * 清理旧日志文件
     */
    async cleanOldLogs() {
        const files = this.getLogFiles();
        
        if (files.length === 0) {
            console.log(chalk.yellow('没有日志文件需要清理'));
            return;
        }

        const { cleanOption } = await inquirer.prompt([
            {
                type: 'list',
                name: 'cleanOption',
                message: '选择清理方式:',
                choices: [
                    { name: '🗑️  删除7天前的文件', value: '7days' },
                    { name: '🗑️  删除30天前的文件', value: '30days' },
                    { name: '🗑️  只保留最新50个文件', value: 'keep50' },
                    { name: '🗑️  删除所有日志文件', value: 'all' },
                    { name: '❌ 取消', value: 'cancel' }
                ]
            }
        ]);

        if (cleanOption === 'cancel') {
            console.log(chalk.yellow('已取消清理操作'));
            return;
        }

        let filesToDelete = [];
        const now = new Date();

        switch (cleanOption) {
            case '7days':
                const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                filesToDelete = files.filter(file => file.modified < sevenDaysAgo);
                break;
            case '30days':
                const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                filesToDelete = files.filter(file => file.modified < thirtyDaysAgo);
                break;
            case 'keep50':
                filesToDelete = files.slice(50); // 保留前50个，删除其余的
                break;
            case 'all':
                filesToDelete = files;
                break;
        }

        if (filesToDelete.length === 0) {
            console.log(chalk.green('没有符合条件的文件需要删除'));
            return;
        }

        console.log(chalk.yellow(`\n将删除 ${filesToDelete.length} 个文件:`));
        filesToDelete.slice(0, 10).forEach(file => {
            console.log(chalk.gray(`  • ${file.name}`));
        });
        
        if (filesToDelete.length > 10) {
            console.log(chalk.gray(`  ... 还有 ${filesToDelete.length - 10} 个文件`));
        }

        const { confirm } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: '确认删除这些文件?',
                default: false
            }
        ]);

        if (!confirm) {
            console.log(chalk.yellow('已取消删除操作'));
            return;
        }

        let deletedCount = 0;
        let failedCount = 0;

        filesToDelete.forEach(file => {
            try {
                fs.unlinkSync(file.path);
                deletedCount++;
            } catch (error) {
                console.error(chalk.red(`删除文件失败 ${file.name}: ${error.message}`));
                failedCount++;
            }
        });

        console.log(chalk.green(`✅ 清理完成: 删除 ${deletedCount} 个文件`));
        if (failedCount > 0) {
            console.log(chalk.red(`❌ 失败: ${failedCount} 个文件`));
        }
    }

    /**
     * 查看日志文件内容
     */
    async viewLogFile() {
        const files = this.getLogFiles();
        
        if (files.length === 0) {
            console.log(chalk.yellow('没有日志文件可查看'));
            return;
        }

        const { fileChoice } = await inquirer.prompt([
            {
                type: 'list',
                name: 'fileChoice',
                message: '选择要查看的日志文件:',
                choices: [
                    ...files.slice(0, 20).map((file, index) => ({
                        name: `${file.name} (${(file.size / 1024).toFixed(2)} KB)`,
                        value: index
                    })),
                    { name: '❌ 取消', value: -1 }
                ]
            }
        ]);

        if (fileChoice === -1) {
            return;
        }

        const selectedFile = files[fileChoice];
        
        try {
            const content = fs.readFileSync(selectedFile.path, 'utf8');
            const data = JSON.parse(content);
            
            console.log(chalk.blue.bold(`\n📄 文件内容: ${selectedFile.name}`));
            console.log('═'.repeat(60));
            console.log(JSON.stringify(data, null, 2));
        } catch (error) {
            console.error(chalk.red(`读取文件失败: ${error.message}`));
        }
    }

    /**
     * 显示日志统计
     */
    showLogStats() {
        const files = this.getLogFiles();
        
        if (files.length === 0) {
            console.log(chalk.yellow('没有日志文件'));
            return;
        }

        const stats = {
            total: files.length,
            batchSummary: 0,
            nftPurchase: 0,
            singlePurchase: 0,
            totalSize: 0
        };

        files.forEach(file => {
            stats.totalSize += file.size;
            
            if (file.name.includes('batch-purchase-summary')) {
                stats.batchSummary++;
            } else if (file.name.includes('nft-purchase')) {
                stats.nftPurchase++;
            } else if (file.name.includes('single-purchase')) {
                stats.singlePurchase++;
            }
        });

        console.log(chalk.blue.bold('\n📊 日志统计:'));
        console.log('═'.repeat(40));
        console.log(chalk.green(`总文件数: ${stats.total}`));
        console.log(chalk.cyan(`批量购买汇总: ${stats.batchSummary}`));
        console.log(chalk.blue(`单个NFT购买: ${stats.nftPurchase}`));
        console.log(chalk.gray(`单次购买: ${stats.singlePurchase}`));
        console.log(chalk.yellow(`总大小: ${(stats.totalSize / 1024 / 1024).toFixed(2)} MB`));
        
        if (files.length > 0) {
            const oldestFile = files[files.length - 1];
            const newestFile = files[0];
            console.log(chalk.gray(`最早记录: ${oldestFile.created.toLocaleString()}`));
            console.log(chalk.gray(`最新记录: ${newestFile.modified.toLocaleString()}`));
        }
    }
}

/**
 * 主菜单
 */
async function main() {
    console.log(chalk.blue.bold('📋 PPP.Fun 日志管理工具\n'));
    
    const logManager = new LogManager();
    
    while (true) {
        const { action } = await inquirer.prompt([
            {
                type: 'list',
                name: 'action',
                message: '选择操作:',
                choices: [
                    { name: '📋 查看日志文件列表', value: 'list' },
                    { name: '📊 显示日志统计', value: 'stats' },
                    { name: '👁️  查看日志文件内容', value: 'view' },
                    { name: '🗑️  清理旧日志文件', value: 'clean' },
                    { name: '❌ 退出', value: 'exit' }
                ]
            }
        ]);

        switch (action) {
            case 'list':
                logManager.displayLogFiles();
                break;
            case 'stats':
                logManager.showLogStats();
                break;
            case 'view':
                await logManager.viewLogFile();
                break;
            case 'clean':
                await logManager.cleanOldLogs();
                break;
            case 'exit':
                console.log(chalk.blue('👋 再见!'));
                process.exit(0);
        }

        console.log('\n' + '─'.repeat(50));
    }
}

// 运行主程序
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export { LogManager };
