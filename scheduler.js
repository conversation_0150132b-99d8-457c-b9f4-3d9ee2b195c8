import chalk from 'chalk';
import fs from 'fs/promises';
import path from 'path';
import { config } from './config.js';
import { PPPProjectNFTBuyer } from './ppp-project-nft-buyer.js';
// 增强版调度器，支持真实购买

class NFTScheduler {
    constructor() {
        this.buyer = null;
        this.isRunning = false;
        this.currentTimeout = null;
        this.projectsData = new Map();
        this.logDir = config.logging.logDir || './logs';
        this.cacheDir = config.cache.dataDir || './cache';
        this.statusFile = path.join(this.cacheDir, 'scheduler-status.json');
        this.startTime = Date.now();
    }

    /**
     * 初始化调度器
     */
    async initialize() {
        console.log(chalk.blue.bold('🚀 NFT定时购买调度器启动'));
        console.log('═'.repeat(50));

        try {
            // 创建必要的目录
            console.log(chalk.blue('📁 创建必要目录...'));
            await this.ensureDirectories();

            // 初始化真实的买家模块
            console.log(chalk.blue('📦 初始化买家模块...'));

            this.buyer = new PPPProjectNFTBuyer();
            await this.buyer.initialize();

            // 自动加载钱包
            console.log(chalk.blue('🔑 加载钱包...'));
            const walletLoaded = this.buyer.walletManager.autoLoadWallet();
            if (!walletLoaded) {
                throw new Error('无法加载钱包，请确保已保存钱包配置');
            }

            // 确保PPP Buy脚本已初始化
            this.buyer.ensurePPPBuyScriptInitialized();

            // 显示钱包信息
            const walletInfo = await this.buyer.getWalletInfo();
            if (walletInfo) {
                console.log(chalk.green(`💼 钱包地址: ${walletInfo.address.substring(0, 8)}...${walletInfo.address.substring(-8)}`));
                console.log(chalk.green(`💰 SOL余额: ${walletInfo.solBalance.toFixed(4)} SOL`));
            } else {
                throw new Error('钱包信息获取失败');
            }

            console.log(chalk.green('✅ 买家模块初始化成功'));
            
            // 获取项目数据
            console.log(chalk.blue('📊 加载项目数据...'));
            await this.loadProjectsData();

            this.isRunning = true;
            console.log(chalk.green('✅ 调度器初始化完成'));

            // 写入初始状态
            await this.updateStatus('running', '调度器已启动，正在等待执行任务');

            // 开始调度
            await this.startScheduling();

        } catch (error) {
            console.error(chalk.red(`❌ 初始化失败: ${error.message}`));
            console.error(chalk.red(`错误堆栈: ${error.stack}`));

            // 写入错误状态
            try {
                await this.updateStatus('error', `初始化失败: ${error.message}`);
            } catch (statusError) {
                console.error(chalk.red(`❌ 写入错误状态失败: ${statusError.message}`));
            }

            process.exit(1);
        }
    }

    /**
     * 确保必要的目录存在
     */
    async ensureDirectories() {
        try {
            await fs.mkdir(this.logDir, { recursive: true });
            await fs.mkdir(this.cacheDir, { recursive: true });
        } catch (error) {
            console.warn(chalk.yellow(`⚠️ 创建目录失败: ${error.message}`));
        }
    }

    /**
     * 加载项目数据
     */
    async loadProjectsData() {
        console.log(chalk.blue('📋 加载项目数据...'));

        for (const projectMint of config.projects) {
            try {
                console.log(chalk.gray(`  🔍 获取项目: ${projectMint.substring(0, 8)}...`));
                
                // 获取项目信息
                const projectInfo = await this.getProjectInfo(projectMint);
                if (!projectInfo) {
                    console.log(chalk.yellow(`  ⚠️ 项目 ${projectMint} 信息获取失败`));
                    continue;
                }

                // 获取NFT列表
                const nfts = await this.getProjectNFTs(projectMint);
                
                // 计算下次购买时间
                const nextBuyTime = this.calculateNextBuyTime(projectInfo);

                this.projectsData.set(projectMint, {
                    info: projectInfo,
                    nfts: nfts,
                    nextBuyTime: nextBuyTime,
                    lastUpdate: Date.now()
                });

                console.log(chalk.green(`  ✅ ${projectInfo.project_name} - 下次购买: ${new Date(nextBuyTime).toLocaleString()}`));

                // 缓存数据
                await this.cacheProjectData(projectMint, {
                    info: projectInfo,
                    nfts: nfts,
                    nextBuyTime: nextBuyTime,
                    lastUpdate: Date.now()
                });

            } catch (error) {
                console.log(chalk.red(`  ❌ 项目 ${projectMint} 加载失败: ${error.message}`));
            }
        }

        console.log(chalk.green(`📊 已加载 ${this.projectsData.size} 个项目`));
    }

    /**
     * 获取项目信息
     */
    async getProjectInfo(projectMint) {
        try {
            const result = await this.buyer.getProjects({ limit: 50, sort: '' });
            const projects = result.data || [];
            
            return projects.find(p => p.project_mint === projectMint);
        } catch (error) {
            console.error(chalk.red(`获取项目信息失败: ${error.message}`));
            return null;
        }
    }

    /**
     * 获取项目NFT列表
     */
    async getProjectNFTs(projectMint) {
        try {
            let allNFTs = [];
            let page = 1;
            const limit = 100;
            
            while (true) {
                const nftResult = await this.buyer.getProjectNFTs(projectMint, {
                    limit: limit,
                    page: page,
                    sort: 'price',
                    order: 'asc'
                });
                
                const pageNFTs = nftResult.data || [];
                if (pageNFTs.length === 0) break;
                
                allNFTs = allNFTs.concat(pageNFTs);
                
                if (pageNFTs.length < limit) break;
                page++;
                
                if (page > 10) break; // 防止无限循环
            }

            // 过滤可购买的NFT (排除自己的NFT)
            const myAddress = this.buyer.walletManager.getAddress();
            const buyableNFTs = allNFTs.filter(nft =>
                nft.price > 0 &&
                nft.owner_pubkey &&
                nft.owner_pubkey !== myAddress
            );

            return buyableNFTs.sort((a, b) => a.price - b.price);
        } catch (error) {
            console.error(chalk.red(`获取NFT列表失败: ${error.message}`));
            return [];
        }
    }

    /**
     * 计算下次购买时间
     */
    calculateNextBuyTime(projectInfo) {
        const lastRound = projectInfo.last_round * 1000; // 转换为毫秒
        const secPerRound = projectInfo.sec_per_round * 1000; // 转换为毫秒
        const advanceMs = config.buyConfig.advanceSeconds * 1000; // 提前时间

        const nextRoundTime = lastRound + secPerRound;
        const nextBuyTime = nextRoundTime - advanceMs;

        // 如果计算出的时间已经过去，则设置为1分钟后（用于测试）
        const now = Date.now();
        if (nextBuyTime <= now) {
            console.log(chalk.yellow(`⚠️ 项目 ${projectInfo.project_name} 的下次购买时间已过期，设置为1分钟后`));
            return now + 60000; // 1分钟后
        }

        return nextBuyTime;
    }

    /**
     * 缓存项目数据
     */
    async cacheProjectData(projectMint, data) {
        if (!config.cache.enabled) return;

        try {
            const cacheFile = path.join(this.cacheDir, `${projectMint}.json`);
            await fs.writeFile(cacheFile, JSON.stringify(data, null, 2));
        } catch (error) {
            console.warn(chalk.yellow(`⚠️ 缓存数据失败: ${error.message}`));
        }
    }

    /**
     * 开始调度
     */
    async startScheduling() {
        console.log(chalk.blue.bold('\n⏰ 开始定时调度'));
        console.log('═'.repeat(50));

        while (this.isRunning) {
            try {
                // 找到最近需要执行的任务
                const nextTask = this.findNextTask();
                
                if (!nextTask) {
                    console.log(chalk.yellow('⚠️ 没有找到待执行的任务，等待60秒后重新检查...'));
                    await this.updateStatus('waiting', '没有待执行任务，等待中...');
                    await this.sleep(60000);
                    continue;
                }

                const { projectMint, nextBuyTime } = nextTask;
                const now = Date.now();
                const waitTime = nextBuyTime - now;

                if (waitTime > 0) {
                    const projectData = this.projectsData.get(projectMint);
                    const projectName = projectData?.info?.project_name || 'Unknown';
                    console.log(chalk.blue(`⏳ 等待 ${Math.round(waitTime / 1000)} 秒后执行项目 ${projectName}... 的购买任务`));

                    // 使用可中断的睡眠，定期更新状态
                    await this.sleepWithStatusUpdate(waitTime, projectMint, projectName);
//                     await this.sleep(5000);
                }

                // 执行购买任务
                await this.executeBuyTask(projectMint);


                // //延时5分钟 重新缓存projectMint 的nft 数据
                // await this.sleep(300000);

                // // 重新获取并缓存该项目的NFT数据
                // console.log(chalk.blue(`🔄 重新缓存项目 ${projectMint.substring(0, 8)}... 的NFT数据`));
                // try {
                //     const updatedNFTs = await this.getProjectNFTs(projectMint);
                //     const projectData = this.projectsData.get(projectMint);
                //     if (projectData) {
                //         projectData.nfts = updatedNFTs;
                //         projectData.lastUpdate = Date.now();

                //         // 更新缓存文件
                //         await this.cacheProjectData(projectMint, projectData);

                //         console.log(chalk.green(`✅ 项目 ${projectMint.substring(0, 8)}... NFT数据已更新 (${updatedNFTs.length} 个可购买NFT)`));
                //     }
                // } catch (error) {
                //     console.log(chalk.yellow(`⚠️ 重新缓存NFT数据失败: ${error.message}`));
                // }

                // 更新下次执行时间
                await this.updateNextBuyTime(projectMint);

            } catch (error) {
                console.error(chalk.red(`❌ 调度错误: ${error.message}`));
                await this.sleep(10000); // 错误后等待10秒
            }
        }
    }

    /**
     * 找到下一个需要执行的任务
     */
    findNextTask() {
        let nextTask = null;
        let earliestTime = Infinity;

        for (const [projectMint, data] of this.projectsData) {
            if (data.nextBuyTime < earliestTime) {
                earliestTime = data.nextBuyTime;
                nextTask = { projectMint, nextBuyTime: data.nextBuyTime };
            }
        }

        return nextTask;
    }

    /**
     * 执行购买任务
     */
    async executeBuyTask(projectMint) {
        const projectData = this.projectsData.get(projectMint);
        const projectName = projectData?.info?.project_name || 'Unknown';

        console.log(chalk.green.bold(`\n🛒 开始执行购买任务: ${projectMint.substring(0, 8)}...`));
        await this.updateStatus('buying', `正在执行 ${projectName} 的购买任务`, {
            currentProject: projectMint
        });
        
        try {
            const projectData = this.projectsData.get(projectMint);
            if (!projectData) {
                throw new Error('项目数据不存在');
            }

            // 重新获取最新的NFT数据
            console.log(chalk.blue('🔄 获取最新NFT数据...'));
            // let latestNFTs = await this.getProjectNFTs(projectMint);
            let latestNFTs = [];

            if (latestNFTs.length === 0) {
                console.log(chalk.yellow('⚠️ 使用缓存NFT数据...'));
                latestNFTs = projectData.nfts;
                // return;
            }

            // 选择要购买的NFT - 使用配置的策略
            const nftCount = Math.min(config.buyConfig.nftCountPerProject, latestNFTs.length);
            const strategy = config.buyConfig.selectionStrategy || 'random';
            const selectedNFTs = this.smartSelectNFTs(latestNFTs, nftCount, strategy);

            console.log(chalk.blue(`📋 选择购买 ${selectedNFTs.length} 个NFT`));
            selectedNFTs.forEach((nft, index) => {
                const priceSol = nft.price / 1000000000;
                console.log(chalk.gray(`  ${index + 1}. NFT ${nft.nft_id} - ${priceSol.toFixed(6)} SOL`));
            });
            //延时26秒 因为提前了30秒 读取项目的nft数据 给4秒时间
            await this.sleep(1*1000);

            // 执行真实购买
            console.log(chalk.blue('🔄 执行真实购买...'));

            // 准备NFT数据
            const nftList = selectedNFTs.map(nft => ({
                creator_pubkey:projectData.info.creator_pubkey,
                project_pubkey:projectData.info.project_pubkey,
                nft_id: nft.nft_id,
                mint_pubkey: nft.mint_pubkey,
                nft_pubkey: nft.nft_pubkey,
                owner_pubkey: nft.owner_pubkey,
                price: nft.price
            }));

            // 使用PPP Buy脚本的批量购买功能 - 打包交易
            const result = await this.buyer.pppBuyScript.executeBatchBuy(nftList, {
                maxNFTsPerTx: config.buyConfig.maxNFTsPerTx,
                maxRetries: config.buyConfig.maxRetries,
                batchMode: config.buyConfig.batchMode,
                concurrencyLimit: config.buyConfig.concurrencyLimit
            });

            // 记录结果
            await this.logBuyResult(projectMint, result);

            console.log(chalk.green(`✅ 购买任务完成: 成功 ${result.successCount}/${result.totalNFTs}`));

            await this.updateStatus('running', `${projectName} 购买完成: ${result.successCount}/${result.totalNFTs}`, {
                lastBuyResult: {
                    projectMint,
                    projectName,
                    successCount: result.successCount,
                    totalNFTs: result.totalNFTs,
                    timestamp: Date.now()
                }
            });

        } catch (error) {
            console.error(chalk.red(`❌ 购买任务失败: ${error.message}`));
            await this.updateStatus('error', `${projectName} 购买失败: ${error.message}`, {
                lastError: {
                    projectMint,
                    projectName,
                    error: error.message,
                    timestamp: Date.now()
                }
            });
            await this.logError(projectMint, error);
        }
    }

    /**
     * 更新下次购买时间
     */
    async updateNextBuyTime(projectMint) {
        try {
            // 重新获取项目信息
            const projectInfo = await this.getProjectInfo(projectMint);
            if (!projectInfo) return;

            const nextBuyTime = this.calculateNextBuyTime(projectInfo);
            
            const projectData = this.projectsData.get(projectMint);
            if (projectData) {
                projectData.nextBuyTime = nextBuyTime;
                projectData.lastUpdate = Date.now();
                
                console.log(chalk.blue(`📅 下次购买时间: ${new Date(nextBuyTime).toLocaleString()}`));
            }

        } catch (error) {
            console.error(chalk.red(`❌ 更新购买时间失败: ${error.message}`));
        }
    }

    /**
     * 记录购买结果
     */
    async logBuyResult(projectMint, result) {
        if (!config.logging.enabled) return;

        try {
            const logData = {
                timestamp: new Date().toISOString(),
                projectMint,
                result,
                type: 'buy_result'
            };

            const logFile = path.join(this.logDir, `scheduler-${new Date().toISOString().split('T')[0]}.log`);
            await fs.appendFile(logFile, JSON.stringify(logData) + '\n');
        } catch (error) {
            console.warn(chalk.yellow(`⚠️ 记录日志失败: ${error.message}`));
        }
    }

    /**
     * 记录错误
     */
    async logError(projectMint, error) {
        if (!config.logging.enabled) return;

        try {
            const logData = {
                timestamp: new Date().toISOString(),
                projectMint,
                error: error.message,
                stack: error.stack,
                type: 'error'
            };

            const logFile = path.join(this.logDir, `scheduler-${new Date().toISOString().split('T')[0]}.log`);
            await fs.appendFile(logFile, JSON.stringify(logData) + '\n');
        } catch (err) {
            console.warn(chalk.yellow(`⚠️ 记录错误日志失败: ${err.message}`));
        }
    }

    /**
     * 更新状态文件
     */
    async updateStatus(status, message, additionalData = {}) {
        try {
            const statusData = {
                pid: process.pid,
                status: status, // 'running', 'waiting', 'buying', 'error', 'stopped'
                message: message,
                startTime: this.startTime,
                lastUpdate: Date.now(),
                projectsCount: this.projectsData.size,
                nextTasks: this.getNextTasksInfo(),
                ...additionalData
            };

            await fs.writeFile(this.statusFile, JSON.stringify(statusData, null, 2));
        } catch (error) {
            console.warn(chalk.yellow(`⚠️ 更新状态文件失败: ${error.message}`));
        }
    }

    /**
     * 获取下几个任务的信息
     */
    getNextTasksInfo() {
        const now = Date.now();
        const tasks = [];

        for (const [projectMint, data] of this.projectsData) {
            const timeUntil = data.nextBuyTime - now;
            const timeUntilStr = timeUntil > 0 ?
                `${Math.round(timeUntil / 1000)} 秒后` :
                '已到期';

            tasks.push({
                projectMint: projectMint.substring(0, 8) + '...',
                projectName: data.info?.project_name || 'Unknown',
                nextBuyTime: data.nextBuyTime,
                nextBuyTimeStr: new Date(data.nextBuyTime).toLocaleString(),
                timeUntil: timeUntil,
                timeUntilStr: timeUntilStr
            });
        }

        return tasks.sort((a, b) => a.nextBuyTime - b.nextBuyTime).slice(0, 5);
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 可中断的睡眠，定期更新状态
     */
    async sleepWithStatusUpdate(totalWaitTime, projectMint, projectName) {
        const startTime = Date.now();
        const updateInterval = 5000; // 每5秒更新一次状态

        while (this.isRunning) {
            const elapsed = Date.now() - startTime;
            const remaining = totalWaitTime - elapsed;

            if (remaining <= 0) {
                break; // 等待时间结束
            }

            // 更新状态，显示剩余时间
            const remainingSeconds = Math.round(remaining / 1000);
            const hours = Math.floor(remainingSeconds / 3600);
            const minutes = Math.floor((remainingSeconds % 3600) / 60);
            const seconds = remainingSeconds % 60;

            let timeStr = '';
            if (hours > 0) {
                timeStr = `${hours}小时${minutes}分${seconds}秒`;
            } else if (minutes > 0) {
                timeStr = `${minutes}分${seconds}秒`;
            } else {
                timeStr = `${seconds}秒`;
            }

            // 在控制台显示倒计时（覆盖上一行）
            process.stdout.write(`\r⏳ 等待 ${timeStr} 后执行 ${projectName} 的购买任务...`);

            await this.updateStatus('waiting', `等待执行 ${projectName} 的购买任务`, {
                waitingFor: projectMint,
                waitTimeSeconds: remainingSeconds,
                nextExecutionTime: startTime + totalWaitTime,
                timeDisplay: timeStr
            });

            // 睡眠一个更新间隔或剩余时间（取较小值）
            const sleepTime = Math.min(updateInterval, remaining);
            await this.sleep(sleepTime);
        }

        // 等待结束，换行确保后续输出格式正确
        console.log(''); // 换行

        // 如果调度器被停止，抛出异常
        if (!this.isRunning) {
            throw new Error('调度器已停止');
        }
    }

    /**
     * 停止调度器
     */
    async stop() {
        this.isRunning = false;
        if (this.currentTimeout) {
            clearTimeout(this.currentTimeout);
        }
        await this.updateStatus('stopped', '调度器已停止');
        console.log(chalk.yellow('⏹️ 调度器已停止'));
    }

    /**
     * 随机选择指定数量的NFT
     * @param {Array} nftList - NFT列表
     * @param {number} count - 要选择的数量
     * @returns {Array} 随机选择的NFT数组
     */
    randomSelectNFTs(nftList, count) {
        if (!nftList || nftList.length === 0) {
            console.log(chalk.yellow('⚠️ NFT列表为空'));
            return [];
        }

        if (count >= nftList.length) {
            console.log(chalk.blue(`📋 选择全部 ${nftList.length} 个NFT (请求数量 >= 可用数量)`));
            return [...nftList]; // 返回副本
        }

        console.log(chalk.blue(`🎲 从 ${nftList.length} 个NFT中随机选择 ${count} 个`));

        // 使用Fisher-Yates洗牌算法进行随机选择
        const shuffled = [...nftList]; // 创建副本避免修改原数组

        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }

        const selected = shuffled.slice(0, count);

        // 按价格排序显示（便于查看，但不影响随机性）
        const sortedForDisplay = [...selected].sort((a, b) => a.price - b.price);

        console.log(chalk.green(`✅ 随机选择完成，按价格排序显示:`));
        sortedForDisplay.forEach((nft, index) => {
            const priceSol = nft.price / 1000000000;
            console.log(chalk.gray(`  ${index + 1}. NFT ${nft.nft_id} - ${priceSol.toFixed(6)} SOL`));
        });

        return selected;
    }

    /**
     * 智能选择NFT - 支持多种选择策略
     * @param {Array} nftList - NFT列表
     * @param {number} count - 要选择的数量
     * @param {string} strategy - 选择策略: 'random', 'cheapest', 'expensive', 'mixed'
     * @returns {Array} 选择的NFT数组
     */
    smartSelectNFTs(nftList, count, strategy = 'random') {
        if (!nftList || nftList.length === 0) {
            console.log(chalk.yellow('⚠️ NFT列表为空'));
            return [];
        }

        if (count >= nftList.length) {
            console.log(chalk.blue(`📋 选择全部 ${nftList.length} 个NFT`));
            return [...nftList];
        }

        let selected = [];

        switch (strategy) {
            case 'cheapest':
                console.log(chalk.blue(`💰 选择最便宜的 ${count} 个NFT`));
                const sortedByCheapest = [...nftList].sort((a, b) => a.price - b.price);
                selected = sortedByCheapest.slice(0, count);
                break;

            case 'expensive':
                console.log(chalk.blue(`💎 选择最贵的 ${count} 个NFT`));
                const sortedByExpensive = [...nftList].sort((a, b) => b.price - a.price);
                selected = sortedByExpensive.slice(0, count);
                break;

            case 'mixed':
                console.log(chalk.blue(`🎯 混合策略选择 ${count} 个NFT`));
                const mixedRatio = config.buyConfig.mixedRatio || { cheap: 0.6, random: 0.4 };
                const cheapCount = Math.floor(count * mixedRatio.cheap);
                const randomCount = count - cheapCount;

                console.log(chalk.gray(`  📊 策略分配: ${cheapCount}个便宜NFT + ${randomCount}个随机NFT`));

                const sortedForMixed = [...nftList].sort((a, b) => a.price - b.price);
                const cheapest = sortedForMixed.slice(0, cheapCount);
                const remaining = sortedForMixed.slice(cheapCount);
                const randomFromRemaining = this.randomSelectNFTs(remaining, randomCount);

                selected = [...cheapest, ...randomFromRemaining];
                break;

            case 'random':
            default:
                selected = this.randomSelectNFTs(nftList, count);
                return selected; // randomSelectNFTs已经包含了日志输出
        }

        // 显示选择结果
        console.log(chalk.green(`✅ ${strategy}策略选择完成:`));
        selected.forEach((nft, index) => {
            const priceSol = nft.price / 1000000000;
            console.log(chalk.gray(`  ${index + 1}. NFT ${nft.nft_id} - ${priceSol.toFixed(6)} SOL`));
        });

        return selected;
    }
}

// 启动调度器
const scheduler = new NFTScheduler();

// 处理程序退出
process.on('SIGINT', () => {
    console.log(chalk.yellow('\n📴 接收到退出信号，正在停止调度器...'));
    scheduler.stop();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log(chalk.yellow('\n📴 接收到终止信号，正在停止调度器...'));
    scheduler.stop();
    process.exit(0);
});

// 启动
scheduler.initialize().catch(error => {
    console.error(chalk.red(`❌ 调度器启动失败: ${error.message}`));
    process.exit(1);
});

