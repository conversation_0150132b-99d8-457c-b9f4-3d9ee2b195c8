import { Connection, PublicKey } from '@solana/web3.js';
import { PPPInstructionBuilder } from './ppp-instruction-builder.js';


async function testburn() {
    console.log('🧪 测试PPP Burn指令10个账户实现');
    console.log('='.repeat(50));

    // 初始化连接和构建器
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    const builder = new PPPInstructionBuilder(connection);

    // 测试参数
    const testParams = {
        project_pubkey: 'CY3iCpr64zP2pBJa3nB3Kt4fuwJ8y1ejUFJTR1avwbpn', // Ani项目 CY3iCpr64zP2pBJa3nB3Kt4fuwJ8y1ejUFJTR1avwbpn 5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp
        mint_pubkey: '5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp', // Ani项目 CY3iCpr64zP2pBJa3nB3Kt4fuwJ8y1ejUFJTR1avwbpn 5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp
        nftId: 711,
        Payer: new PublicKey('7WeMeL1nMeqdwzrHfVcA6SQen9ZyhhUBUtNBkT3VAL3b'),
    };




    // 构建账户列表
    console.log('🔧 构建账户列表...');
    const buildResult = await builder.bulidBurnInstructionAccounts(testParams);

    console.log(`${JSON.stringify(buildResult)}`);
}


/**
 * 测试PPP Buy指令的25个账户实现
 */
async function test25AccountsImplementation() {
    console.log('🧪 测试PPP Buy指令25个账户实现');
    console.log('='.repeat(50));

    // 初始化连接和构建器
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    const builder = new PPPInstructionBuilder(connection);

    // 测试参数
    const testParams = {
        project_pubkey: 'CY3iCpr64zP2pBJa3nB3Kt4fuwJ8y1ejUFJTR1avwbpn', // Ani项目 CY3iCpr64zP2pBJa3nB3Kt4fuwJ8y1ejUFJTR1avwbpn 5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp
        mint_pubkey: '5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp', // Ani项目 CY3iCpr64zP2pBJa3nB3Kt4fuwJ8y1ejUFJTR1avwbpn 5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp
        nftId: 254,
        buyer: new PublicKey('7WeMeL1nMeqdwzrHfVcA6SQen9ZyhhUBUtNBkT3VAL3b'),
        owner_pubkey: '3hAx5KRJHwah7QW8pWbtjDU6rzGRagGvF72wNbEEkKef',
        creator: 'U9cGTCZGhMEwtjoyX1cB2LL7Sq1FCsNhAsVTCB5Judy',
        referral: null // 测试可选账户
    };

    try {
        console.log('📋 测试参数:');
        console.log(`  项目代币: ${testParams.mint_pubkey}`);
        console.log(`  NFT ID: ${testParams.nftId}`);
        console.log(`  买家: ${testParams.buyer.toString()}`);
        console.log(`  前拥有者: ${testParams.owner_pubkey}`);
        console.log(`  创建者: ${testParams.creator}`);
        console.log(`  推荐人: ${testParams.referral || '无'}`);
        console.log();

        // 构建账户列表
        console.log('🔧 构建账户列表...');
        const buildResult = await builder.buildBuyInstructionAccounts(testParams);

        console.log(`${JSON.stringify(buildResult)}`);


        console.log('✅ 构建完成!');
        console.log();

        // 生成调试信息
        const debugInfo = builder.generateDebugInfo(buildResult);

        // 显示摘要
        console.log('📊 账户摘要:');
        // console.log(`  总账户数: ${debugInfo.summary.totalAccounts}/${debugInfo.summary.expectedAccounts}`);
        // console.log(`  签名者数量: ${debugInfo.summary.signerCount}`);
        // console.log(`  可写账户数: ${debugInfo.summary.writableCount}`);
        // console.log(`  是否有效: ${debugInfo.summary.isValid ? '✅' : '❌'}`);
        // console.log();

        // 显示关键PDA地址
        // console.log('🔑 关键PDA地址:');
        // console.log(`  项目PDA: ${debugInfo.pdaAddresses.projectPda}`);
        // console.log(`  NFT PDA: ${debugInfo.pdaAddresses.nftPda}`);
        // console.log(`  池权限PDA: ${debugInfo.pdaAddresses.poolAuthPda}`);
        // console.log();

        // // 显示Raydium账户
        // console.log('🌊 Raydium账户:');
        // console.log(`  权限PDA: ${debugInfo.raydiumAccounts.authority}`);
        // console.log(`  池状态: ${debugInfo.raydiumAccounts.poolState}`);
        // console.log(`  输入金库: ${debugInfo.raydiumAccounts.inputVault}`);
        // console.log(`  输出金库: ${debugInfo.raydiumAccounts.outputVault}`);
        // console.log(`  观察状态: ${debugInfo.raydiumAccounts.observation}`);
        // console.log();

        // // 显示代币账户
        // console.log('💰 代币账户:');
        // console.log(`  买家WSOL: ${debugInfo.tokenAccounts.buyerWsol}`);
        // console.log(`  买家项目代币: ${debugInfo.tokenAccounts.buyerProject}`);
        // console.log(`  池WSOL金库: ${debugInfo.tokenAccounts.poolWsolVault}`);
        // console.log();

        // 验证账户配置
        console.log('🔍 验证账户配置...');
        const validation = await builder.validateAccounts(buildResult.accounts);

        if (validation.issues.length === 0) {
            console.log('✅ 验证通过 - 所有账户配置正确!');
        } else {
            console.log('❌ 验证发现问题:');
            validation.issues.forEach((issue, index) => {
                console.log(`  ${index + 1}. ${issue}`);
            });
        }
        console.log();

        // 显示详细账户列表
        console.log('📋 详细账户列表 (IDL映射):');
        console.log('-'.repeat(100));
        console.log('索引 | IDL名称                | 完整地址                                         | 签名 | 可写');
        console.log('-'.repeat(100));

        debugInfo.accountList.forEach(acc => {
            const index = acc.index.toString().padStart(2, ' ');
            const name = acc.idlName.padEnd(20, ' ');
            const pubkey = acc.pubkey; // 显示完整地址
            const signer = acc.isSigner ? '✓' : ' ';
            const writable = acc.isWritable ? '✓' : ' ';

            console.log(`${index}   | ${name} | ${pubkey} | ${signer}    | ${writable}`);
        });
        console.log('-'.repeat(100));

        // 测试结果
        console.log();
        console.log('🎯 测试结果:');
        if (debugInfo.summary.isValid && validation.issues.length === 0) {
            console.log('✅ 测试通过 - PPP Buy指令25个账户实现正确!');
            console.log('✅ 所有账户都按照IDL规范正确配置');
            console.log('✅ 账户顺序、权限和可写性都符合要求');
        } else {
            console.log('❌ 测试失败 - 需要修复以下问题:');
            if (!debugInfo.summary.isValid) {
                console.log(`  - 账户数量不正确: ${debugInfo.summary.totalAccounts}/25`);
            }
            validation.issues.forEach(issue => {
                console.log(`  - ${issue}`);
            });
        }

        return {
            success: debugInfo.summary.isValid && validation.issues.length === 0,
            accountCount: debugInfo.summary.totalAccounts,
            issues: validation.issues,
            buildResult,
            debugInfo
        };

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 运行测试
async function main() {
    try {
        const result = await test25AccountsImplementation();
        console.log('\n🏁 测试完成');


        const result1 = await testburn();
        console.log(`${JSON.stringify(result1)}`);
    } catch (error) {
        console.error('💥 测试运行失败:', error);
        process.exit(1);
    }
}

// 直接运行测试
main();

export { test25AccountsImplementation };

