#!/usr/bin/env node

import { PPPDataProvider } from './data-provider.js';
import chalk from 'chalk';
import inquirer from 'inquirer';
import fs from 'fs';

/**
 * PPP数据管理工具
 * 用于批量获取、管理和分析项目和NFT数据
 */

async function main() {
    console.log(chalk.blue.bold('📊 PPP数据管理工具\n'));

    const dataProvider = new PPPDataProvider();
    await dataProvider.initialize();

    const { action } = await inquirer.prompt([
        {
            type: 'list',
            name: 'action',
            message: '选择操作:',
            choices: [
                { name: '📋 获取所有项目数据', value: 'all_projects' },
                { name: '🔥 获取热门项目', value: 'hot_projects' },
                { name: '🆕 获取最新项目', value: 'latest_projects' },
                { name: '🔍 搜索项目', value: 'search_projects' },
                { name: '🎨 获取项目NFT数据', value: 'project_nfts' },
                { name: '💰 获取最便宜NFT', value: 'cheapest_nfts' },
                { name: '⏰ 获取最新交易NFT', value: 'recent_nfts' },
                { name: '📊 项目统计分析', value: 'project_stats' },
                { name: '💾 批量数据导出', value: 'bulk_export' },
                { name: '🗑️  清除缓存', value: 'clear_cache' },
                { name: '❌ 退出', value: 'exit' }
            ]
        }
    ]);

    try {
        switch (action) {
            case 'all_projects':
                await getAllProjectsData(dataProvider);
                break;
            case 'hot_projects':
                await getHotProjects(dataProvider);
                break;
            case 'latest_projects':
                await getLatestProjects(dataProvider);
                break;
            case 'search_projects':
                await searchProjects(dataProvider);
                break;
            case 'project_nfts':
                await getProjectNFTs(dataProvider);
                break;
            case 'cheapest_nfts':
                await getCheapestNFTs(dataProvider);
                break;
            case 'recent_nfts':
                await getRecentNFTs(dataProvider);
                break;
            case 'project_stats':
                await getProjectStats(dataProvider);
                break;
            case 'bulk_export':
                await bulkExport(dataProvider);
                break;
            case 'clear_cache':
                dataProvider.clearCache();
                break;
            case 'exit':
                console.log(chalk.blue('👋 再见!'));
                process.exit(0);
                break;
        }
    } catch (error) {
        console.error(chalk.red(`操作失败: ${error.message}`));
    }

    // 询问是否继续
    const { continue: shouldContinue } = await inquirer.prompt([
        {
            type: 'confirm',
            name: 'continue',
            message: '是否继续其他操作?',
            default: true
        }
    ]);

    if (shouldContinue) {
        await main();
    }
}

/**
 * 获取所有项目数据
 */
async function getAllProjectsData(dataProvider) {
    const { limit } = await inquirer.prompt([
        {
            type: 'number',
            name: 'limit',
            message: '获取项目数量:',
            default: 50,
            validate: (value) => value > 0 && value <= 200 || '请输入1-200之间的数字'
        }
    ]);

    const projects = await dataProvider.getAllProjects({ limit });
    
    if (projects.length > 0) {
        console.log(chalk.green.bold(`\n✅ 成功获取 ${projects.length} 个项目:`));
        
        projects.slice(0, 10).forEach((project, index) => {
            dataProvider.displayProjectSummary(project);
        });

        if (projects.length > 10) {
            console.log(chalk.gray(`\n... 还有 ${projects.length - 10} 个项目未显示`));
        }

        await askToSave(dataProvider, 'projects', () => dataProvider.saveProjectsData());
    }
}

/**
 * 获取热门项目
 */
async function getHotProjects(dataProvider) {
    const projects = await dataProvider.getHotProjects(20);
    
    if (projects.length > 0) {
        console.log(chalk.green.bold(`\n🔥 热门项目 (按24h交易量排序):`));
        
        projects.forEach((project, index) => {
            console.log(chalk.yellow(`${index + 1}. `), end='');
            dataProvider.displayProjectSummary(project);
        });

        await askToSave(dataProvider, 'hot-projects', () => dataProvider.saveProjectsData('hot-projects.json'));
    }
}

/**
 * 获取最新项目
 */
async function getLatestProjects(dataProvider) {
    const projects = await dataProvider.getLatestProjects(20);
    
    if (projects.length > 0) {
        console.log(chalk.green.bold(`\n🆕 最新项目:`));
        
        projects.forEach((project, index) => {
            console.log(chalk.yellow(`${index + 1}. `), end='');
            dataProvider.displayProjectSummary(project);
        });

        await askToSave(dataProvider, 'latest-projects', () => dataProvider.saveProjectsData('latest-projects.json'));
    }
}

/**
 * 搜索项目
 */
async function searchProjects(dataProvider) {
    const { keyword } = await inquirer.prompt([
        {
            type: 'input',
            name: 'keyword',
            message: '输入搜索关键词:',
            validate: (value) => value.length > 0 || '请输入关键词'
        }
    ]);

    const projects = await dataProvider.searchProjects(keyword);
    
    if (projects.length > 0) {
        console.log(chalk.green.bold(`\n🔍 搜索结果 "${keyword}":`));
        
        projects.forEach((project, index) => {
            console.log(chalk.yellow(`${index + 1}. `), end='');
            dataProvider.displayProjectSummary(project);
        });

        await askToSave(dataProvider, 'search-results', () => dataProvider.saveProjectsData(`search-${keyword}.json`));
    } else {
        console.log(chalk.yellow('未找到相关项目'));
    }
}

/**
 * 获取项目NFT数据
 */
async function getProjectNFTs(dataProvider) {
    const { projectMint, limit } = await inquirer.prompt([
        {
            type: 'input',
            name: 'projectMint',
            message: '输入项目mint地址:',
            validate: (value) => value.length > 0 || '请输入项目地址'
        },
        {
            type: 'number',
            name: 'limit',
            message: '获取NFT数量:',
            default: 50,
            validate: (value) => value > 0 && value <= 500 || '请输入1-500之间的数字'
        }
    ]);

    const nfts = await dataProvider.getProjectNFTs(projectMint, { limit });
    
    if (nfts.length > 0) {
        console.log(chalk.green.bold(`\n✅ 成功获取 ${nfts.length} 个NFT:`));
        
        nfts.slice(0, 10).forEach((nft, index) => {
            dataProvider.displayNFTSummary(nft);
        });

        if (nfts.length > 10) {
            console.log(chalk.gray(`\n... 还有 ${nfts.length - 10} 个NFT未显示`));
        }

        await askToSave(dataProvider, 'NFTs', () => dataProvider.saveNFTsData(projectMint));
    }
}

/**
 * 获取最便宜NFT
 */
async function getCheapestNFTs(dataProvider) {
    const { projectMint } = await inquirer.prompt([
        {
            type: 'input',
            name: 'projectMint',
            message: '输入项目mint地址:',
            validate: (value) => value.length > 0 || '请输入项目地址'
        }
    ]);

    const nfts = await dataProvider.getCheapestNFTs(projectMint, 20);
    
    if (nfts.length > 0) {
        console.log(chalk.green.bold(`\n💰 最便宜的NFT (按价格排序):`));
        
        nfts.forEach((nft, index) => {
            console.log(chalk.yellow(`${index + 1}. `), end='');
            dataProvider.displayNFTSummary(nft);
        });
    }
}

/**
 * 获取最新交易NFT
 */
async function getRecentNFTs(dataProvider) {
    const { projectMint } = await inquirer.prompt([
        {
            type: 'input',
            name: 'projectMint',
            message: '输入项目mint地址:',
            validate: (value) => value.length > 0 || '请输入项目地址'
        }
    ]);

    const nfts = await dataProvider.getRecentNFTs(projectMint, 20);
    
    if (nfts.length > 0) {
        console.log(chalk.green.bold(`\n⏰ 最近交易的NFT:`));
        
        nfts.forEach((nft, index) => {
            console.log(chalk.yellow(`${index + 1}. `), end='');
            dataProvider.displayNFTSummary(nft);
            console.log(chalk.gray(`   最后交易: ${new Date(nft.last_trade * 1000).toLocaleString()}`));
        });
    }
}

/**
 * 获取项目统计
 */
async function getProjectStats(dataProvider) {
    const { projectMint } = await inquirer.prompt([
        {
            type: 'input',
            name: 'projectMint',
            message: '输入项目mint地址:',
            validate: (value) => value.length > 0 || '请输入项目地址'
        }
    ]);

    const stats = await dataProvider.getProjectStats(projectMint);
    
    if (stats) {
        console.log(chalk.green.bold(`\n📊 项目统计信息:`));
        console.log(chalk.blue(`总NFT数量: ${stats.total}`));
        console.log(chalk.green(`活跃NFT: ${stats.active}`));
        console.log(chalk.red(`已销毁NFT: ${stats.burned}`));
        console.log(chalk.yellow(`总价值: ${stats.totalValue.toFixed(4)} SOL`));
        console.log(chalk.yellow(`平均价格: ${stats.averagePrice.toFixed(6)} SOL`));
        console.log(chalk.yellow(`价格范围: ${stats.minPrice.toFixed(6)} - ${stats.maxPrice.toFixed(6)} SOL`));
        console.log(chalk.blue(`总分割次数: ${stats.totalSplits}`));
    }
}

/**
 * 批量导出
 */
async function bulkExport(dataProvider) {
    const { exportType } = await inquirer.prompt([
        {
            type: 'list',
            name: 'exportType',
            message: '选择导出类型:',
            choices: [
                { name: '📋 所有项目数据', value: 'all_projects' },
                { name: '🔥 热门项目数据', value: 'hot_projects' },
                { name: '🎨 指定项目的NFT数据', value: 'project_nfts' }
            ]
        }
    ]);

    switch (exportType) {
        case 'all_projects':
            await dataProvider.saveProjectsData('all-projects-export.json');
            break;
        case 'hot_projects':
            const hotProjects = await dataProvider.getHotProjects(50);
            await dataProvider.saveProjectsData('hot-projects-export.json');
            break;
        case 'project_nfts':
            const { projectMint } = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'projectMint',
                    message: '输入项目mint地址:',
                    validate: (value) => value.length > 0 || '请输入项目地址'
                }
            ]);
            await dataProvider.saveNFTsData(projectMint, `project-${projectMint.substring(0, 8)}-export.json`);
            break;
    }
}

/**
 * 询问是否保存
 */
async function askToSave(dataProvider, dataType, saveFunction) {
    const { save } = await inquirer.prompt([
        {
            type: 'confirm',
            name: 'save',
            message: `是否保存${dataType}数据到文件?`,
            default: true
        }
    ]);

    if (save) {
        await saveFunction();
    }
}

// 错误处理
process.on('uncaughtException', (error) => {
    console.log(chalk.red(`❌ 未捕获的异常: ${error.message}`));
    process.exit(1);
});

process.on('unhandledRejection', (reason) => {
    console.log(chalk.red(`❌ 未处理的Promise拒绝: ${reason}`));
    process.exit(1);
});

// 启动程序
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export { main };
