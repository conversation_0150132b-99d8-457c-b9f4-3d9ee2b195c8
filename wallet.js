import { Connection, Keypair, PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { getAssociatedTokenAddress, getAccount } from '@solana/spl-token';
import { CONFIG } from './config/config.js';
import fs from 'fs';
import path from 'path';

export class WalletManager {
  constructor() {
    this.connection = new Connection(CONFIG.RPC_URL, 'confirmed');
    this.keypair = null;
    this.publicKey = null;
    this.walletConfigPath = './wallet-config.json';
    this.autoLoadWallet();
  }

  // 从私钥初始化钱包
  initFromPrivateKey(privateKey) {
    try {
      // 支持多种私钥格式
      let secretKey;

      if (typeof privateKey === 'string') {
        privateKey = privateKey.trim();

        if (privateKey.startsWith('[') && privateKey.endsWith(']')) {
          // JSON 数组格式: [1,2,3,...]
          secretKey = new Uint8Array(JSON.parse(privateKey));
        } else if (privateKey.includes(',')) {
          // 逗号分隔格式: 1,2,3,...
          const numbers = privateKey.split(',').map(n => parseInt(n.trim()));
          secretKey = new Uint8Array(numbers);
        } else if (privateKey.length >= 87 && privateKey.length <= 88) {
          // Base58 格式 (Solana 标准格式)
          try {
            secretKey = this.base58ToUint8Array(privateKey);
          } catch (error) {
            throw new Error(`Base58 解码失败: ${error.message}. 请使用 JSON 数组格式或检查私钥是否正确。`);
          }
        } else if (privateKey.length === 128) {
          // 十六进制格式
          secretKey = this.hexToUint8Array(privateKey);
        } else {
          throw new Error('Unsupported private key format. Supported formats: Base58, JSON array [1,2,3,...], comma-separated numbers, or hex string');
        }
      } else if (Array.isArray(privateKey)) {
        secretKey = new Uint8Array(privateKey);
      } else {
        throw new Error('Unsupported private key format');
      }

      // 验证密钥长度
      if (secretKey.length !== 64) {
        throw new Error(`Invalid secret key length: ${secretKey.length}. Expected 64 bytes.`);
      }

      this.keypair = Keypair.fromSecretKey(secretKey);
      this.publicKey = this.keypair.publicKey;

      console.log(`钱包已初始化: ${this.publicKey.toString()}`);
      return true;
    } catch (error) {
      console.error('钱包初始化失败:', error.message);
      return false;
    }
  }

  // Base58 解码辅助方法 (改进版)
  base58ToUint8Array(base58String) {
    const ALPHABET = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';
    const BASE = 58;

    // 创建字符到数值的映射
    const ALPHABET_MAP = {};
    for (let i = 0; i < ALPHABET.length; i++) {
      ALPHABET_MAP[ALPHABET[i]] = i;
    }

    if (base58String.length === 0) return new Uint8Array(0);

    // 计算前导零的数量
    let leadingZeros = 0;
    for (let i = 0; i < base58String.length && base58String[i] === '1'; i++) {
      leadingZeros++;
    }

    // 转换为大整数
    let num = 0n;
    for (let i = 0; i < base58String.length; i++) {
      const char = base58String[i];
      if (!(char in ALPHABET_MAP)) {
        throw new Error(`Invalid character in Base58 string: ${char}`);
      }
      num = num * BigInt(BASE) + BigInt(ALPHABET_MAP[char]);
    }

    // 转换为字节数组
    const bytes = [];
    while (num > 0n) {
      bytes.unshift(Number(num % 256n));
      num = num / 256n;
    }

    // 添加前导零
    for (let i = 0; i < leadingZeros; i++) {
      bytes.unshift(0);
    }

    return new Uint8Array(bytes);
  }

  // 十六进制解码辅助方法
  hexToUint8Array(hexString) {
    if (hexString.length % 2 !== 0) {
      throw new Error('Hex string must have even length');
    }

    const result = new Uint8Array(hexString.length / 2);
    for (let i = 0; i < hexString.length; i += 2) {
      result[i / 2] = parseInt(hexString.substr(i, 2), 16);
    }
    return result;
  }

  // 获取 SOL 余额
  async getSolBalance() {
    try {
      if (!this.publicKey) {
        throw new Error('钱包未初始化');
      }

      const balance = await this.connection.getBalance(this.publicKey);
      return balance / LAMPORTS_PER_SOL;
    } catch (error) {
      console.error('获取 SOL 余额失败:', error.message);
      return 0;
    }
  }

  // 获取代币余额
  async getTokenBalance(mintAddress) {
    try {
      if (!this.publicKey) {
        throw new Error('钱包未初始化');
      }

      const mint = new PublicKey(mintAddress);
      const tokenAccount = await getAssociatedTokenAddress(mint, this.publicKey);
      
      try {
        const accountInfo = await getAccount(this.connection, tokenAccount);
        return Number(accountInfo.amount);
      } catch (error) {
        // 如果账户不存在，返回 0
        if (error.name === 'TokenAccountNotFoundError') {
          return 0;
        }
        throw error;
      }
    } catch (error) {
      console.error(`获取代币余额失败 (${mintAddress}):`, error.message);
      return 0;
    }
  }

  // 获取所有代币余额
  async getAllTokenBalances(tokens) {
    const balances = {};
    
    // 获取 SOL 余额
    balances.SOL = await this.getSolBalance();
    
    // 获取其他代币余额
    for (const [symbol, token] of Object.entries(tokens)) {
      if (symbol !== 'SOL') {
        const balance = await this.getTokenBalance(token.mint);
        balances[symbol] = balance / Math.pow(10, token.decimals);
      }
    }
    
    return balances;
  }

  // 检查钱包是否有足够的余额
  async checkSufficientBalance(tokenMint, amount, decimals) {
    try {
      let balance;
      
      if (tokenMint === 'So11111111111111111111111111111111111111112') {
        // SOL
        balance = await this.getSolBalance();
        // 保留最小余额用于交易费用
        return balance >= (amount + CONFIG.MIN_SOL_BALANCE);
      } else {
        // SPL 代币
        const rawBalance = await this.getTokenBalance(tokenMint);
        balance = rawBalance / Math.pow(10, decimals);
        return balance >= amount;
      }
    } catch (error) {
      console.error('检查余额失败:', error.message);
      return false;
    }
  }

  // 获取钱包地址
  getAddress() {
    return this.publicKey ? this.publicKey.toString() : null;
  }

  // 获取 Keypair (用于签名交易)
  getKeypair() {
    return this.keypair;
  }

  // 获取连接对象
  getConnection() {
    return this.connection;
  }

  // 自动加载已保存的钱包
  autoLoadWallet() {
    try {
      if (fs.existsSync(this.walletConfigPath)) {
        const config = JSON.parse(fs.readFileSync(this.walletConfigPath, 'utf8'));

        if (config.encryptedKey && config.address) {
          // 解密并加载钱包
          const secretKey = this.decryptKey(config.encryptedKey);
          this.keypair = Keypair.fromSecretKey(new Uint8Array(secretKey));
          this.publicKey = this.keypair.publicKey;

          console.log(`🔑 自动加载钱包: ${this.publicKey.toString()}`);
          return true;
        }
      }
    } catch (error) {
      console.log('⚠️  自动加载钱包失败，需要手动初始化');
    }
    return false;
  }

  // 保存钱包配置
  saveWalletConfig() {
    try {
      if (!this.keypair) {
        throw new Error('钱包未初始化');
      }

      const config = {
        address: this.publicKey.toString(),
        encryptedKey: this.encryptKey(Array.from(this.keypair.secretKey)),
        timestamp: new Date().toISOString(),
        network: this.connection.rpcEndpoint
      };

      fs.writeFileSync(this.walletConfigPath, JSON.stringify(config, null, 2));
      console.log('✅ 钱包配置已保存');
      return true;
    } catch (error) {
      console.error('❌ 保存钱包配置失败:', error.message);
      return false;
    }
  }

  // 删除保存的钱包配置
  clearSavedWallet() {
    try {
      if (fs.existsSync(this.walletConfigPath)) {
        fs.unlinkSync(this.walletConfigPath);
        console.log('🗑️  已清除保存的钱包配置');
      }

      // 清除内存中的钱包
      this.keypair = null;
      this.publicKey = null;

      return true;
    } catch (error) {
      console.error('❌ 清除钱包配置失败:', error.message);
      return false;
    }
  }

  // 检查是否有保存的钱包
  hasSavedWallet() {
    return fs.existsSync(this.walletConfigPath);
  }

  // 获取保存的钱包信息
  getSavedWalletInfo() {
    try {
      if (fs.existsSync(this.walletConfigPath)) {
        const config = JSON.parse(fs.readFileSync(this.walletConfigPath, 'utf8'));
        return {
          address: config.address,
          timestamp: config.timestamp,
          network: config.network
        };
      }
    } catch (error) {
      console.error('获取钱包信息失败:', error.message);
    }
    return null;
  }

  // 简单的密钥加密 (基于Base64和简单混淆)
  encryptKey(secretKeyArray) {
    // 注意: 这是一个简单的混淆方法，不是真正的加密
    // 在生产环境中应该使用更安全的加密方法
    const mixed = secretKeyArray.map((byte, index) => byte ^ (index % 256));
    return Buffer.from(mixed).toString('base64');
  }

  // 简单的密钥解密
  decryptKey(encryptedKey) {
    try {
      const mixed = Array.from(Buffer.from(encryptedKey, 'base64'));
      const original = mixed.map((byte, index) => byte ^ (index % 256));
      return original;
    } catch (error) {
      throw new Error('解密密钥失败');
    }
  }

  // 重新初始化钱包 (保存新钱包)
  initFromPrivateKeyAndSave(privateKey) {
    const success = this.initFromPrivateKey(privateKey);
    if (success) {
      this.saveWalletConfig();
    }
    return success;
  }
}

// 创建全局钱包实例
export const wallet = new WalletManager();
