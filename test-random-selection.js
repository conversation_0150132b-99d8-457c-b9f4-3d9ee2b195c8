import chalk from 'chalk';
import { config } from './config.js';

/**
 * 测试随机选择NFT功能
 */
class NFTSelectionTester {
    constructor() {
        this.testData = this.generateTestNFTs(100); // 生成100个测试NFT
    }

    /**
     * 生成测试NFT数据
     */
    generateTestNFTs(count) {
        const nfts = [];
        for (let i = 1; i <= count; i++) {
            nfts.push({
                nft_id: `NFT_${i.toString().padStart(3, '0')}`,
                price: Math.floor(Math.random() * 1000000000) + 100000000, // 0.1-1.1 SOL
                mint: `mint_${i}`,
                owner: `owner_${i}`
            });
        }
        // 按价格排序，便于测试
        return nfts.sort((a, b) => a.price - b.price);
    }

    /**
     * 随机选择NFT (Fisher-Yates洗牌算法)
     */
    randomSelectNFTs(nftList, count) {
        if (!nftList || nftList.length === 0) {
            console.log(chalk.yellow('⚠️ NFT列表为空'));
            return [];
        }

        if (count >= nftList.length) {
            console.log(chalk.blue(`📋 选择全部 ${nftList.length} 个NFT`));
            return [...nftList];
        }

        console.log(chalk.blue(`🎲 从 ${nftList.length} 个NFT中随机选择 ${count} 个`));

        // 使用Fisher-Yates洗牌算法
        const shuffled = [...nftList];
        
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }

        return shuffled.slice(0, count);
    }

    /**
     * 智能选择NFT
     */
    smartSelectNFTs(nftList, count, strategy = 'random') {
        if (!nftList || nftList.length === 0) {
            return [];
        }

        if (count >= nftList.length) {
            return [...nftList];
        }

        let selected = [];

        switch (strategy) {
            case 'cheapest':
                console.log(chalk.blue(`💰 选择最便宜的 ${count} 个NFT`));
                const sortedByCheapest = [...nftList].sort((a, b) => a.price - b.price);
                selected = sortedByCheapest.slice(0, count);
                break;

            case 'expensive':
                console.log(chalk.blue(`💎 选择最贵的 ${count} 个NFT`));
                const sortedByExpensive = [...nftList].sort((a, b) => b.price - a.price);
                selected = sortedByExpensive.slice(0, count);
                break;

            case 'mixed':
                console.log(chalk.blue(`🎯 混合策略选择 ${count} 个NFT`));
                const mixedRatio = config.buyConfig.mixedRatio || { cheap: 0.6, random: 0.4 };
                const cheapCount = Math.floor(count * mixedRatio.cheap);
                const randomCount = count - cheapCount;
                
                console.log(chalk.gray(`  📊 策略分配: ${cheapCount}个便宜NFT + ${randomCount}个随机NFT`));
                
                const sortedForMixed = [...nftList].sort((a, b) => a.price - b.price);
                const cheapest = sortedForMixed.slice(0, cheapCount);
                const remaining = sortedForMixed.slice(cheapCount);
                const randomFromRemaining = this.randomSelectNFTs(remaining, randomCount);
                
                selected = [...cheapest, ...randomFromRemaining];
                break;

            case 'random':
            default:
                selected = this.randomSelectNFTs(nftList, count);
                break;
        }

        return selected;
    }

    /**
     * 显示选择结果
     */
    displayResults(selected, title) {
        console.log(chalk.green(`\n✅ ${title}:`));
        selected.forEach((nft, index) => {
            const priceSol = nft.price / 1000000000;
            console.log(chalk.gray(`  ${index + 1}. ${nft.nft_id} - ${priceSol.toFixed(6)} SOL`));
        });

        // 统计信息
        const prices = selected.map(nft => nft.price);
        const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);

        console.log(chalk.cyan(`📊 统计: 平均 ${(avgPrice/1000000000).toFixed(6)} SOL, 最低 ${(minPrice/1000000000).toFixed(6)} SOL, 最高 ${(maxPrice/1000000000).toFixed(6)} SOL`));
    }

    /**
     * 运行所有测试
     */
    async runTests() {
        console.log(chalk.blue.bold('🧪 NFT随机选择功能测试\n'));
        
        const selectCount = 10;
        
        console.log(chalk.yellow(`📋 测试数据: ${this.testData.length} 个NFT, 选择 ${selectCount} 个\n`));

        // 测试1: 随机选择
        console.log(chalk.magenta('='.repeat(50)));
        console.log(chalk.magenta('测试1: 随机选择'));
        const randomSelected = this.smartSelectNFTs(this.testData, selectCount, 'random');
        this.displayResults(randomSelected, '随机选择结果');

        // 测试2: 最便宜选择
        console.log(chalk.magenta('\n' + '='.repeat(50)));
        console.log(chalk.magenta('测试2: 最便宜选择'));
        const cheapestSelected = this.smartSelectNFTs(this.testData, selectCount, 'cheapest');
        this.displayResults(cheapestSelected, '最便宜选择结果');

        // 测试3: 最贵选择
        console.log(chalk.magenta('\n' + '='.repeat(50)));
        console.log(chalk.magenta('测试3: 最贵选择'));
        const expensiveSelected = this.smartSelectNFTs(this.testData, selectCount, 'expensive');
        this.displayResults(expensiveSelected, '最贵选择结果');

        // 测试4: 混合策略
        console.log(chalk.magenta('\n' + '='.repeat(50)));
        console.log(chalk.magenta('测试4: 混合策略'));
        const mixedSelected = this.smartSelectNFTs(this.testData, selectCount, 'mixed');
        this.displayResults(mixedSelected, '混合策略选择结果');

        // 测试5: 多次随机选择验证随机性
        console.log(chalk.magenta('\n' + '='.repeat(50)));
        console.log(chalk.magenta('测试5: 随机性验证 (3次随机选择)'));
        
        for (let i = 1; i <= 3; i++) {
            console.log(chalk.blue(`\n第${i}次随机选择:`));
            const randomTest = this.smartSelectNFTs(this.testData, 5, 'random');
            this.displayResults(randomTest, `第${i}次随机选择`);
        }

        console.log(chalk.green.bold('\n🎉 所有测试完成!'));
    }
}

// 运行测试
const tester = new NFTSelectionTester();
tester.runTests().catch(console.error);
